<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3163_29313)">
<g filter="url(#filter0_f_3163_29313)">
<circle cx="14" cy="12" r="4" fill="#DAFCDA"/>
</g>
<path d="M5.00065 3.33203L17.5006 3.33203C17.9609 3.33203 18.334 3.70513 18.334 4.16536V9.9987L16.6673 9.9987V4.9987L5.00065 4.9987V7.4987L0.833984 4.16536L5.00065 0.832031V3.33203ZM15.0006 16.6654H2.50065C2.04042 16.6654 1.66732 16.2923 1.66732 15.832L1.66732 9.9987H3.33398L3.33398 14.9987L15.0006 14.9987V12.4987L19.1673 15.832L15.0006 19.1654L15.0006 16.6654Z" fill="black"/>
</g>
<defs>
<filter id="filter0_f_3163_29313" x="8" y="6" width="12" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_3163_29313"/>
</filter>
<clipPath id="clip0_3163_29313">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
