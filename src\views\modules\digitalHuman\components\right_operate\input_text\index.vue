<template>
    <div class="right_operate_drive_text_captions">
        <captions ref="captions_ref" @emit_data="emit_data"></captions>
        <chooseMusic ref="choose_music_ref" ></chooseMusic>
        <chooseDub ref="choose_dub_ref"></chooseDub>
        <div class="right_operate_drive_text_captions_btn">
            <el-button v-if="!previewAudio" @click="save" :disabled="isGeneratingAudio">
                生成音频并试听
            </el-button>
            <div v-if="previewAudio" class="regenerate">
                <span @click="save">重新生成</span>
                <div><img :src="playAudioBtnStatus?isPlayAudio[0]:isPlayAudio[1]" @click="toggleAudio" alt="" style="margin: 0 auto;" /></div>
                <p></p>
                <img src="@/assets/images/digitalHuman/right/clear.png" @click="replay" />
                <audio ref="myAudio" :src="audioSrc"></audio>
            </div>
        </div>
        <!-- 全屏loading遮罩层 -->
        <div class="full_screen_loading" v-if="isGeneratingAudio">
            <div class="full_screen_loading_bar_boxs">
                <div
                v-for="(n, index) in 6"
                :key="index"
                :class="['full_screen_loading_bar', { active: index === full_loading_active_index }]"
                ></div>
            </div>
            <span>正在生成音频，请稍后...</span>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive,defineExpose,inject,watch,onUnmounted,nextTick  } from 'vue'
import captions from '@/views/modules/digitalHuman/components/right_operate/input_text/captions.vue'
import chooseDub from '@/views/modules/digitalHuman/components/right_operate/input_text/choose_dub.vue'
import chooseMusic from '@/views/modules/digitalHuman/components/right_operate/choose_music.vue'
import {createVideo} from '@/api/digitalHuman.js'
import { useloginStore } from '@/stores/login'
import { ElMessage } from 'element-plus'
// 导入数字人状态管理store
import { useDigitalHumanStore } from '../../../store/digitalHumanStore'
let loginStore = useloginStore()
// 初始化数字人状态管理store
const digitalHumanStore = useDigitalHumanStore()
let choose_music_ref=ref(null)
let choose_dub_ref=ref(null)
let captions_ref=ref(null)
let input_text_obj=ref({})
let digital_human_right_option = inject('digital_human_right_option');
let request_video=ref({})
// 添加音频生成加载状态
let isGeneratingAudio=ref(false)
// 全屏loading相关变量
let full_loading_active_index = ref(0)
let full_loading_timer = null
// 生成视频并试听按钮功能切换
const previewAudio = ref(false)  
const playAudioBtnStatus = ref(true)
const isPlayAudio = reactive([new URL('@/assets/images/digitalHuman/right/play.png', import.meta.url).href,new URL('@/assets/images/digitalHuman/right/pause.png', import.meta.url).href])    
const audioSrc = ref('')
const myAudio = ref(null)
let get_data=()=>{
    input_text_obj.value={
        captions:{
            textInfo:captions_ref.value.textInfo,
            open_captions:captions_ref.value.open_captions,
        },
        choose_music:{
            current_music:choose_music_ref.value.current_music
        },
        choose_dub:{
            current_character:choose_dub_ref.value.current_character,
            intonation:choose_dub_ref.value.intonation,
            speech:choose_dub_ref.value.speech,
            volume:choose_dub_ref.value.volume
        }
    }
}
let set_data=(data)=>{
    let captions_ref_key=['textInfo','open_captions']
    captions_ref_key.map((item)=>{
        captions_ref.value[item]=data.captions[item]
    })
    captions_ref.value.updateEditorContent()
    choose_music_ref.value.current_music=data.choose_music.current_music
    let choose_dub_key=['current_character','intonation','speech','volume']
    choose_dub_key.map((item)=>{
         choose_dub_ref.value[item]=data.choose_dub[item]
    })
}
const playAudioEvent = () => {
    audioSrc.value = request_video.value?.audio_file || request_video.value?.original_audio_url || ''
    nextTick(()=>{
        if (myAudio.value) {
            myAudio.value.addEventListener('canplay', () => {
                playAudioBtnStatus.value = true;
                myAudio.value.play();
            }, { once: true });
            myAudio.value.addEventListener('ended', () => {
                playAudioBtnStatus.value = false;
            });
            if (myAudio.value.readyState >= 3) {
                playAudioBtnStatus.value = true;
                myAudio.value.play();
            }
        }
    })
}

const toggleAudio = () => {
    if (myAudio.value) {
        if (playAudioBtnStatus.value) {
            myAudio.value.pause()
        } else {
            myAudio.value.play()
        }
        playAudioBtnStatus.value = !playAudioBtnStatus.value
    }
}
const replay = () => {
    audioSrc.value = request_video.value?.audio_file || request_video.value?.original_audio_url || ''
    if (myAudio.value) {
        myAudio.value.currentTime = 0; 
        myAudio.value.play(); 
        playAudioBtnStatus.value = true;
    }
}
let save=async()=>{
    if (myAudio.value) {
        myAudio.value.pause()
        playAudioBtnStatus.value = false;
    }
    get_data()
    if (!input_text_obj.value.captions.textInfo) return ElMessage.error('请输入字幕内容');
    if (!input_text_obj.value.choose_dub.current_character) return ElMessage.error('请选择配音角色');
    // 开始生成音频，显示加载状态
    isGeneratingAudio.value = true;
    // 启动全屏loading动画定时器
    full_loading_timer = setInterval(() => {
        full_loading_active_index.value = (full_loading_active_index.value + 1) % 6;
    }, 300);
    try {
        // 🧹 在确认文本时清除之前的字幕缓存数据
        try {
            digitalHumanStore.clearSubtitleData();
        } catch (error) {
            console.error('❌ 字幕缓存清理失败:', error);
            // 即使清理失败也继续执行，不影响主要功能
        }
        let data=await createVideo({
            userId:loginStore?.userId|| '',
            voiceId: input_text_obj.value.choose_dub.current_character.character_id,
            text: input_text_obj.value.captions.textInfo,
            speed: parseFloat(input_text_obj.value.choose_dub.speech),//语速
            vol:1,//背景音乐音量
            volTts: parseFloat(input_text_obj.value.choose_dub.volume/100),//合成音量
            pitch: parseFloat(input_text_obj.value.choose_dub.intonation),//语调
            bgmiAudioUrl:choose_music_ref.value?.current_music?.bgm_url||''//背景音乐
        })
        request_video.value=data.data
        // 🎵 调试：检查createVideo API返回的数据结构
        console.log('🎵 createVideo API返回数据:', {
            'audio_file': data.data?.audio_file,
            'original_audio_url': data.data?.original_audio_url,
            'audio_name': data.data?.audio_name,
            'extra_info': data.data?.extra_info,
            'subtitle_json': data.data?.subtitle_json,  // 🔧 新增：检查字幕JSON数组
            'subtitle_file': data.data?.subtitle_file   // 🔧 新增：检查字幕文件
        });
        //这里需要试听生成的音频 
        emit_data()
        playAudioEvent()
    } catch (error) {
        console.error('❌ 音频生成失败:', error);
        ElMessage.error('音频生成失败，请重试');
    } finally {
        // 无论成功失败都要隐藏加载状态
        isGeneratingAudio.value = false;
        previewAudio.value = true
        // 清除定时器
        if (full_loading_timer) {
            clearInterval(full_loading_timer);
            full_loading_timer = null;
        }
    }
}
let emit_data=()=>{
    get_data()
    // 检查必要的数据是否存在，避免访问null对象
    const hasCharacter = input_text_obj.value?.choose_dub?.current_character?.info;
    const characterInfo = hasCharacter ? input_text_obj.value.choose_dub.current_character.info : {};
    digital_human_right_option({
        type:'text_captions',//text_captions输入文本   aduio_captions音频驱动
        aduio_data:request_video.value,//接口返回的音频数据  original_audio_url接口生成的音频文件   subtitle_file接口生成的字幕文件  subtitle_json//字幕文件json数组
        open_captions:input_text_obj.value?.captions?.open_captions ?? true,//是否开启字幕 🎯 优化：使用??确保默认为true
        choose_music:input_text_obj.value?.choose_music?.current_music,//选择的背景音乐  具体数据bgm_url当前选择的背景音乐路径   info当前背景音乐接口返回所有数据   current_nav切换类型1推荐音乐2我的音乐  current_classify当current_nav为1时下方的具体类型
        // 🎬 新增：传递字幕JSON数组到右侧操作面板
        subtitle_json: request_video.value?.subtitle_json || [],//接口返回的字幕JSON数组，用于生成视频时的字幕处理
        audioJson:{
            type: "tts",//文本传tts，音频驱动传audio
            tts: {
                text: [input_text_obj.value?.captions?.textInfo || ""],
                speed: parseFloat(input_text_obj.value?.choose_dub?.speech || 1.0), //语速
                audio_man: "",
                pitch: parseFloat(input_text_obj.value?.choose_dub?.intonation || 100) //语调
            },
            // 🎵 播放器使用audio_file字段，从createVideo API返回数据中获取
            wav_url: request_video.value?.audio_file || request_video.value?.original_audio_url || '',
            wav_name: request_video.value?.audio_name || "",
            wav_text: input_text_obj.value?.captions?.textInfo || "",
            // 🔧 修复：从createVideo API返回数据中获取音频时长
            duration: request_video.value?.extra_info?.audio_length || 0,
            volume: Math.round(input_text_obj.value?.choose_dub?.volume || 100), //音量
            language: "cn",
            voiceId: characterInfo.id || 1,
            voicePerson: characterInfo.voiceName || "默认音色",//音色名称
            voiceImg: characterInfo.avatarUrl || "",
            // 🎬 新增：将字幕JSON数组添加到audioJson中，确保在构建保存参数时能访问到
            subtitle_json: request_video.value?.subtitle_json || []//字幕JSON数组，包含详细的时间轴和文本信息
        }
    })
}
// 🔧 监听字幕开关和背景音乐变化
watch(() => ({ a: captions_ref.value?.open_captions, b:choose_music_ref.value?.current_music }), (newVal, oldVal) => {
    digital_human_right_option({
        type:'text_captions',//text_captions输入文本   aduio_captions音频驱动
        aduio_data:request_video.value,//接口返回的音频数据  original_audio_url接口生成的音频文件   subtitle_file接口生成的字幕文件  subtitle_json//字幕文件json数组
        open_captions:input_text_obj.value?.captions?.open_captions,//是否开启字幕
        choose_music:input_text_obj.value?.choose_music?.current_music//选择的背景音乐  具体数据bgm_url当前选择的背景音乐路径   info当前背景音乐接口返回所有数据   current_nav切换类型1推荐音乐2我的音乐  current_classify当current_nav为1时下方的具体类型
    })
  },
  { deep: true } // 如果监听对象内部深层变化需要加deep
);
// 🔧 新增：监听文本内容变化，实时更新字幕显示
watch(() => captions_ref.value?.textInfo, (newText, oldText) => {
    // 只有当文本真正发生变化且不为空时才触发
    if (newText !== oldText && newText && newText.trim() !== '') {
        get_data(); // 更新input_text_obj
        // 构建包含文本内容的数据对象
        const textCaptionsData = {
            type:'text_captions',
            aduio_data:request_video.value,
            open_captions:input_text_obj.value?.captions?.open_captions || false,
            choose_music:input_text_obj.value?.choose_music?.current_music,
            subtitle_json: request_video.value?.subtitle_json || [],
            audioJson:{
                type: "tts",
                tts: {
                    text: [newText],
                    speed: parseFloat(input_text_obj.value?.choose_dub?.speech || 1.0),
                    audio_man: "",
                    pitch: parseFloat(input_text_obj.value?.choose_dub?.intonation || 100)
                },
                wav_url: request_video.value?.audio_file || request_video.value?.original_audio_url || '',
                wav_name: request_video.value?.audio_name || "",
                wav_text: newText,
                duration: request_video.value?.extra_info?.audio_length || 0,
                volume: Math.round(input_text_obj.value?.choose_dub?.volume || 100),
                language: "cn",
                voiceId: input_text_obj.value?.choose_dub?.current_character?.info?.id || 1,
                voicePerson: input_text_obj.value?.choose_dub?.current_character?.info?.voiceName || "默认音色",
                voiceImg: input_text_obj.value?.choose_dub?.current_character?.info?.avatarUrl || "",
                subtitle_json: request_video.value?.subtitle_json || []
            }
        };
        digital_human_right_option(textCaptionsData);
    }
}, { immediate: false });
// 组件销毁时清理定时器
onUnmounted(() => {
    if (full_loading_timer) {
        clearInterval(full_loading_timer);
        full_loading_timer = null;
    }
});
defineExpose({
    input_text_obj,
    get_data,
    set_data
})
</script>
<style lang="scss" scoped>
.right_operate_drive_text_captions{
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}
.right_operate_drive_text_captions_btn{
    align-self:center;
    margin-top: auto;
    width: 100%;
    padding: 0 18px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    .el-button{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 36px;
        background-color: #0AAF60;
        border-radius: 2px;
        ::v-deep(span){
            font-size: 14px;
            color: #FFFFFF;
        }

        // 禁用状态样式
        &:disabled {
            opacity: 0.8;
            cursor: not-allowed;
        }
    }
    .regenerate{
        width: 100%;
        height: 36px;
        background: #0AAF60;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #FFFFFF;
        border-radius: 2px;
        div{
            width: 30px;
            height:100%;
            display: flex;
            align-items: center;
        }
        span{
            display: inline-block;
            width: 185px;
            text-align: center;
            cursor: pointer;
        }
        p{
            width: 1px;
            height: 10px;
            background: rgba(239, 239, 241, 0.2);
            margin: 0 20px;
        }
        img{
            cursor: pointer;
        }
    }
}

// 全屏loading样式
.full_screen_loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 半透明遮罩 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  .full_screen_loading_bar_boxs {
	display: flex;
	align-items: center;
	gap: 7px;
	height:41px;
	margin-bottom: 58px;
    .full_screen_loading_bar {
		width: 6px;
		height: 25px;
		background-color: #80F1AD; /* 默认浅绿色 */
		border-radius: 3px;
		animation-name: waveColor;
		animation-duration: 1.8s;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
		transform-origin: center center;
		&:nth-child(1) {
			animation-delay: 0s;
		}
		&:nth-child(2) {
			animation-delay: 0.3s;
		}
		&:nth-child(3) {
			animation-delay: 0.6s;
		}
		&:nth-child(4) {
			animation-delay: 0.9s;
		}
		&:nth-child(5) {
			animation-delay: 1.2s;
		}
		&:nth-child(6) {
			animation-delay: 1.5s;
		}
	}
 }
	span{
		font-weight: 500;
		font-size: 15px;
		line-height: 21px;
		letter-spacing: -0.02em;
		color: #FFFFFF;
	}
}

/* 波浪动画关键帧 */
@keyframes waveColor {
  0%, 100% {
    transform: scaleY(1);
    background-color: #80F1AD; /* 浅绿色 */
    opacity: 1;
  }
  50% {
    transform: scaleY(1.8);
    background-color: #0AAF60; /* 深绿色 */
    opacity: 1;
  }
}
</style>