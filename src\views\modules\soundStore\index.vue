<template>
<div class="soundStore">
    <Headbar />
    <div class="soundStore_contanier">
        <search ref="search_ref" @search_list="search_list"></search>
        <filterTag ref="filter_tag_ref" @changeNav="getFilterTag" @changesubNav="changesubNav"></filterTag> 
        <div class="list_filter">
            <!-- <div class="list_filter_total">
                为您找到{{dubbing_artist_num}}个配音师，共{{dubbing_style_num}}种风格
            </div>
            <i class="segmentation"></i> -->
            <!-- <div class="soundStore_sort">
                <span class="soundStore_sort_title">排序方式：</span>
                <span class="soundStore_sort_item" @click="choose_sort(item.key)" :class="item.key==current_sort?'current':''" v-for="(item,index) in soundStore_sort" :key="index">{{ item.label }}</span>
            </div>
            <i class="segmentation"></i> -->
            <div class="soundStore_collect">
                <el-checkbox-group v-model="selectedFilters" @change="handleFilterChange">
                    <el-checkbox label="buy" key="buy" value="buy">只看已购</el-checkbox>
                    <el-checkbox label="collect" key="collect" value="collect">只看收藏</el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
        <soundList ref="sound_list_ref"></soundList>
    </div>
</div>
<!-- <Footer></Footer> -->
</template>
<script setup>
import search from  './compoments/search.vue'
import filterTag from  './compoments/filter_tag.vue'
import {reactive,ref,onMounted,provide,onActivated, nextTick,getCurrentInstance } from "vue"
import soundList from  './compoments/sound_list.vue'
import Footer from '@/views/modules/mainPage/components/footer/index.vue'
import { getAll,getAllMetadata,bookmarkList,queryVoiceWithPackage,query,queryUserBuyVoiceName } from '@/api/soundStore.js'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
import { ElMessage } from "element-plus";
import { useloginStore } from '@/stores/login'
import { useSoundStore } from '@/stores/modules/soundStore.js' 
import { useRoute } from 'vue-router';
import axios from 'axios';
const { proxy } = getCurrentInstance();
let route = useRoute();
let soundStore = useSoundStore()
let loginStore = useloginStore() 
let dubbing_artist_num=ref(978)
let dubbing_style_num=ref(1636)
let search_ref=ref(null)
let filter_tag_ref=ref(null)
let selectedFilters = ref([])
let only_buy=ref(false)
let sound_list_ref=ref(null)
let listData=ref([])//存储列表数据
let tts=ref()
const cities = ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen']
let soundStore_sort=reactive([
    {
        key:'hot',
        label:'最热'
    },
    {
        key:'new',
        label:'最新'
    }
])
let current_sort=ref("hot")
let choose_sort=(key)=>{
    if(key=='new'){
        let dataCopy =JSON.parse(JSON.stringify(listData.value))
        dataCopy.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
        search_ref.value.list=JSON.parse(JSON.stringify(dataCopy))
    }
    current_sort.value=key
}
let changesubNav=(data)=>{
    sound_list_ref.value.close_aduio()
    handleClick(data)
    // getList(data,'changesubNav')
}
let splitAndFilter = (value) => {
    // console.log(value,'splitAndFilter');
        if(value){
            return value.split('、').map(item => item.trim());
        }else{
            return []
        }
       
};
let filterData = (data, filters) => {
        return data.filter(item => {
            return Object.keys(filters).every(key => {
                // 检查过滤条件是否存在
                const filterValue = filters[key]; // 直接获取过滤值
                if (!filterValue || filterValue === '') {
                    return true; // 如果没有该条件，则跳过筛选
                }

                let itemValues = [];

                // 根据映射关系获取对应的 item 值
                if (key === 'gender') {
                    itemValues = splitAndFilter(item.gender).concat(splitAndFilter(item.ageGroup));
                } else if (key === 'sceneCategory') {
                    itemValues = splitAndFilter(item.sceneCategory);
                } else if (key === 'recommendTags') {
                    itemValues = splitAndFilter(item.recommendTags);
                } else if (key === 'grade') {
                    itemValues = splitAndFilter(item.membershipGrade);
                }

                // 如果当前过滤值为 '全部'，则不进行筛选
                if (filterValue === '全部') {
                    return true; // 不筛选当前标签
                }

                // 检查 itemValues 是否包含 filterValue
                return itemValues.includes(filterValue);
            });
        });
    };
    let navKeys=ref([{
            key:'grade',
            nav_key:'grade',
            arr_key:'grade',
        },{
            key:'gender',
            nav_key:'genders',
            arr_key:'gender',
        },{
            key:'sceneCategory',
            nav_key:'sceneMetadata',
            arr_key:'scene_category',
        },{
            key:'recommendTags',
            nav_key:'recommend_tags',
            arr_key:'recommend_tags',
        }])
let handleClick = (filters) => {
    
        let selectLastIndex=filter_tag_ref.value.selectLastIndex
        let selectedKeys = navKeys.value.slice(selectLastIndex + 1);
        selectedKeys.map((item)=>{
            Object.keys(filters).map((item1)=>{
                if(item.key==item1){
                   delete filters[item1]
                }
            })
        })
        sound_list_ref.value.init([])
        nextTick(()=>{
            const filteredData = filterData(listData.value, filters); // 筛选数据
            // console.log(filteredData,listData.value,filters,'filteredData');
            sound_list_ref.value.init(JSON.parse(JSON.stringify(filteredData)))
            search_ref.value.list=JSON.parse(JSON.stringify(filteredData))
            
            let nav_data=getNav(filteredData); // 更新 the_data
            // render_nav(nav_data)
            nextTick(()=>{
           

        
                let tags=filter_tag_ref.value.tags
                let index1 = tags.findIndex((item) => item.type == filter_tag_ref.value['current_type'])
                let tagsData=tags[index1]
               
                // console.log(selectedKeys,nav_data,'selectedKeys');
                // selectedKeys.map((item)=>{
                //     tagsData.condition[item.key].arr=nav_data[item.nav_key].map((item1)=>{
                //         return {
                //             key: item1[item.arr_key],
                //             label: item1[item.arr_key],
                //         };
                //     })
                // })
                selectedKeys.map((item) => {
                    tagsData.condition[item.key].arr = nav_data[item.nav_key]
                        .filter((item1) => item1[item.arr_key] !== '') // 过滤出 item1[item.arr_key] 不等于 ''
                        .map((item1) => {
                            return {
                                key: item1[item.arr_key],
                                label: item1[item.arr_key],
                            };
                        });
                });

                
                filter_tag_ref.value.init()
    
                filter_tag_ref.value.updateDom()
                // render_nav(nav_data)
                console.log(tagsData,'handleClick'); // 输出更新后的数据
        
        })
        })
       
    };
let getNav=(list_data)=>{
    // console.log(list_data,'list_data');
    const extractUnique = (data, key, transformFunc) => {
        const uniqueSet = new Set();
        data.forEach(item => {
            // 获取原始值
            const value = transformFunc(item);
            if (value) {
                // 如果值包含逗号，则分割并添加到集合中
                value.split('、').forEach(val => {
                    uniqueSet.add(val.trim()); // 去掉空格并添加到集合中
                });
            }
        });
        return Array.from(uniqueSet).map(value => ({ [key]: value }));
    };


    // 使用 Set 来存储唯一的 gender 和 ageGroup
    const uniqueGenders = new Set();

    // 处理所有层
    list_data.forEach(item => {
        // 分割 gender 和 ageGroup
        const genders = item.gender.split('、');
        const ageGroups = item.ageGroup.split('、');

        // 将每个分割后的值添加到 uniqueGenders
        genders.forEach(gender => {
            uniqueGenders.add(gender.trim());
        });

        ageGroups.forEach(ageGroup => {
            uniqueGenders.add(ageGroup.trim());
        });
    });

    // 将唯一的值转换为对象并存入 the_data.genders
    const the_data = {};
    the_data.genders = Array.from(uniqueGenders).map(gender => ({ gender }));

    // 根据 list_data 中的顺序提取 genderOrder，并去重
    const genderOrder = Array.from(new Set(list_data.flatMap(item => item.gender.split('、').map(g => g.trim()))));

    // 定义 the_genders
    let the_genders = ['男', '女'];

    // 排序 genderOrder
    const sortedGenders = genderOrder.sort((a, b) => {
        const indexA = the_genders.indexOf(a);
        const indexB = the_genders.indexOf(b);
        
        // 如果在 the_genders 中找不到，返回 1，使其排到后面
        if (indexA === -1 && indexB === -1) return 0; // 都不在 the_genders 中
        if (indexA === -1) return 1; // a 不在 the_genders 中，排到后面
        if (indexB === -1) return -1; // b 不在 the_genders 中，排到后面
        
        return indexA - indexB; // 按照 the_genders 的顺序排序
    });
    // 将排序后的结果存入 the_data
    the_data.genders = sortedGenders.map(gender => ({ gender }));

    // 提取其他信息
    const sceneCategoryData = extractUnique(list_data, 'scene_category', item => item.sceneCategory);
    const recommendTagData = extractUnique(list_data, 'recommend_tags', item => item.recommendTags);
    const membershipGradeData = extractUnique(list_data, 'grade', item => item.membershipGrade);
// console.log(sceneCategoryData,'sceneCategoryData');

    // 将提取的结果合并到 the_data
    the_data.sceneMetadata = sceneCategoryData;
    the_data.recommend_tags = recommendTagData;
    the_data.grade = membershipGradeData;
    return the_data
}
let render_nav=(nav_list,data,init)=>{
       console.log('render_nav');
    nextTick(async()=>{
   
   let { genders,sceneMetadata,grade,recommend_tags }=nav_list
   let tags=filter_tag_ref.value.tags
   // filter_tag_ref.value.sceneMetadata=sceneMetadata
 
   
   tags.map((item) => {
       Object.keys(item.condition).map((item1)=>{
           item.condition[item1].arr=[]
       })
   })
   let tagsData= {}
   if(data){
    if(data==4){
        tagsData=tags[0]
    }else{
        tagsData=tags[1] 
    }
   }else{
    let index1 = tags.findIndex((item) => item.type == filter_tag_ref.value['current_type'])
    // console.log(tags,index1,'tags');
    
    tagsData=tags[index1]
   }
   
   tagsData.condition[Object.keys(tagsData.condition)[0]].arr = grade
    .filter(item => item.grade !== '') // 过滤出不为空的项
    .map(item => {
        return {
            key: item.grade,
            label: item.grade,
        };
    });
    tagsData.condition[Object.keys(tagsData.condition)[1]].arr = genders
    .filter(item => item.gender !== '') // 过滤出不为空的项
    .map(item => {
        return {
            key: item.gender,
            label: item.gender,
        };
    });
    tagsData.condition[Object.keys(tagsData.condition)[2]].arr = sceneMetadata
    .filter(item => item.scene_category !== '') // 过滤出不为空的项
    .map(item => {
        return {
            key: item.scene_category,
            label: item.scene_category,
        };
    });
    tagsData.condition[Object.keys(tagsData.condition)[3]].arr = recommend_tags
    .filter(item => item.recommend_tags !== '') // 过滤出不为空的项
    .map(item => {
        return {
            key: item.recommend_tags,
            label: item.recommend_tags,
        };
    });

   filter_tag_ref.value.uniqueArray=JSON.parse(JSON.stringify(tagsData.condition))
   Object.keys(filter_tag_ref.value.uniqueArray).map((item)=>{
    const exists = filter_tag_ref.value.uniqueArray[item].arr.some(item => item.key === '全部');
        if(!exists){
            filter_tag_ref.value.uniqueArray[item].arr.unshift({
                key: '全部',
                label: '全部'
            })
        }
    
   })
   console.log(tagsData,'tagsData');
   
   filter_tag_ref.value.init()
  
  filter_tag_ref.value.updateDom()
})
}
let getFilterTag=async(data)=>{
    tts.value=data
    sound_list_ref.value.tts=data
    selectedFilters.value=[]
    filter_tag_ref.value.current_type=data
    filter_tag_ref.value.tag_loading=true
    // sound_list_ref.value.list=[]
    sound_list_ref.value.list_loading=true
         console.log('getFilterTag');
    let list_data=await getList()

    let nav_list=getNav(list_data)
    filter_tag_ref.value.uniqueArray=nav_list
    filter_tag_ref.value.selectLastIndex=null

    try {
        render_nav(nav_list,data)
       
    } catch (error) {
        filter_tag_ref.value.tag_loading=false
        // console.log(error,'error');
        // ElMessage.error('数据加载失败，请刷新页面重试');
        if(error.message){
        
            ElMessage({ message:error.message , type: 'error' });
        }
    }
}
let getList=async(nav={},type='')=>{
    // console.log(nav);
    
    return new Promise(async(resolve,reject)=>{
    sound_list_ref.value.close_aduio()
// 如果之前的请求存在，则取消它
    if (sound_list_ref.value.cancelToken) {
        sound_list_ref.value.cancelToken.cancel(' ');
    }
    // 创建新的 CancelToken 源并存储在 sound_list_ref 中
    sound_list_ref.value.cancelToken = axios.CancelToken.source();
    // sound_list_ref.value.list=[]

    sound_list_ref.value.list_loading=true
    let list_pack_data=[]
    try {
        let list_single_data=[]
        // if(type=='changesubNav'){

        //     // list_single_data=await query({tts: filter_tag_ref.value.current_type,...nav, cancelToken: sound_list_ref.value.cancelToken.token })
        // }else{
            list_single_data=await getAll({userId:loginStore.userId,tts: filter_tag_ref.value.current_type,  cancelToken: sound_list_ref.value.cancelToken.token })
        // }
        // console.log(list_single_data,'list_single_data');
        // let languageSkillsSort = ["中文", "英文"];
        // list_single_data.sort((a, b) => {
        //     return (languageSkillsSort.indexOf(a.languageSkills) - languageSkillsSort.indexOf(b.languageSkills));
        // });
        // if(filter_tag_ref.value.current_type==4){
        //     let membershipGradeSort=["SVIP",'VIP']
  
        //     list_single_data.sort((a, b) => {
        //         return (membershipGradeSort.indexOf(a.membershipGrade) - membershipGradeSort.indexOf(b.membershipGrade));
        //     });
        // }
        // 调用抽离出来的单条数据排序函数
        sortSingleData(list_single_data, filter_tag_ref.value.current_type);
        if(filter_tag_ref.value.current_type==5){
            let data=await queryVoiceWithPackage({voiceType:"SFT",inUse:"5", cancelToken: sound_list_ref.value.cancelToken.token,userId:loginStore.userId})
            list_pack_data=data.reduce((acc, item) => {
                // console.log(acc,item,999);
                
                let packageInfo= item.packageInfo
                const type = packageInfo.type; 
     
                
                let group = acc.find(g => g.platformNickname === type);
                if (!group) {
                    group = {...packageInfo,packageType:item.packageType, platformNickname: type, data: [],  package: true, };
                    acc.push(group);
                }
                let item_data= { ...item }
                delete item_data.packageInfo; 
                group.data.push(item_data);
                group.area=item.recommendTags+item.sceneCategory
                let keys=['gender','ageGroup','sceneCategory','recommendTags','membershipGrade']
                keys.map((item1)=>{
                    if(item[item1]){
                        group[item1]=item[item1]
                    }else{
                        group[item1]=''
                    }
                    
                })
                console.log(acc,group,type,999);
                return acc;
            }, []);
            list_pack_data.map(group => {
                // 判断 group.data 中所有项目的 isBuy 是否都是 1
                const allBought = group.data.every(item => item.isBuy === 1);
                group.is_buy = allBought ? 1 : 0;
                group.data.sort((a, b) => {
                    //添加的 price 排序逻辑
                    if (a.price !== b.price) {
                        return b.price - a.price; // price 倒序
                    }
                    // 最后按 recommendDegree 排序，值小的在前
                    return a.recommendDegree - b.recommendDegree;
                })
            });
           console.log(list_pack_data,'list_pack_data');
           
            // 找到“进阶版套餐包”组
            const advanceIndex = list_pack_data.findIndex(item => item.type === '进阶版套餐包');
            if (advanceIndex !== -1) {
            // 收集所有非“进阶版套餐包”的数据，扁平化合并成一个数组
            const otherData = list_pack_data
                .filter(item => item.type !== '进阶版套餐包')
                .flatMap(item => item.data);

            // 深拷贝“进阶版套餐包”的数据和其他数据，合并后赋值
            list_pack_data[advanceIndex].data = [
                ...JSON.parse(JSON.stringify(list_pack_data[advanceIndex].data)),
                ...JSON.parse(JSON.stringify(otherData))
            ];
            }
           
            soundStore.setAllPackage(list_pack_data)
        }
      
       
        
     
        
        let all_data=[...list_pack_data,...list_single_data]
        console.log(all_data,'列表渲染2');
        try {
         sound_list_ref.value.init(JSON.parse(JSON.stringify(all_data)));
        // 这里是异步加载成功后的代码
        } catch (error) {
        console.error('调用 init 出错:', error);
        // 这里处理异常，比如提示用户或重试
        }
        // sound_list_ref.value.init(JSON.parse(JSON.stringify(all_data)))
        console.log('列表渲染3');
        search_ref.value.list=JSON.parse(JSON.stringify(all_data))
        sound_list_ref.value.all_data=JSON.parse(JSON.stringify(all_data))
        listData.value=JSON.parse(JSON.stringify(all_data))
        tts.value=filter_tag_ref.value.current_type
        resolve(all_data)
        
    } catch (error) {
        sound_list_ref.value.list_loading=false
        // console.log(error,'error');
        // if(error.response.data){
        //     ElMessage({ message:error.response.data , type: 'error' });
        // }
        // ElMessage.error('数据加载失败，请刷新页面重试');
        if (axios.isCancel(error)) {
            // console.log('请求被取消:', error.message); // 处理请求被取消的情况
        } else {
            // console.error('请求失败:', error); // 处理其他错误
        }
    }
})
}
let handleFilterChange=async(event)=>{

    console.log(event,'event');
    
    if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        // 延迟清空，避免事件冲突
        await nextTick()
        selectedFilters.value = []
        return
    }
    // 单选限制：只保留最后一个选中项
    if (event.length > 1) {
        await nextTick()
        selectedFilters.value = [event[event.length - 1]]
    }
    console.log( selectedFilters.value,'selectedFilters.value');
    sound_list_ref.value.close_aduio()
    if(selectedFilters.value.length>0){
        sound_list_ref.value.list_loading=true
        let data=[]
      
        console.log(selectedFilters.value[0],'collect');
        try {
            if(selectedFilters.value[0]=='collect'){
           
                data=await bookmarkList({tts:filter_tag_ref.value.current_type,userId: loginStore.userId,})
            }else if(selectedFilters.value[0]=='buy'){
              let result=await queryUserBuyVoiceName({tts:filter_tag_ref.value.current_type,userId: loginStore.userId,})
             
              data = Array.isArray(result?.content?.result) ? result.content.result : []
              console.log(data,'列表渲染');
            //   if( data.length>0){
            //     data=data.map(item=>{
            //         return {
            //            ... item.info
            //         }
            //     })
            //   }
            }
           
            
            data=keysToCamelCase(data)
            data.map((item)=>{
                item.isBuy='1'
            })
            data=sortSingleData(data, filter_tag_ref.value.current_type)
            sound_list_ref.value.init(JSON.parse(JSON.stringify(data)))
            search_ref.value.list=JSON.parse(JSON.stringify(data))
            listData.value=JSON.parse(JSON.stringify(data))
        }catch (error) {
            sound_list_ref.value.list_loading=false
            // console.log(error,'error');
            // ElMessage({ '操作失败，请重试', type: 'error' });
            // ElMessage.error('数据加载失败，请刷新页面重试');
        }
      
    }
    else{
        getList()
    }
}
let toCamelCase=(str)=>{
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}
let keysToCamelCase=(obj)=>{
  if (Array.isArray(obj)) {
    return obj.map(v => keysToCamelCase(v))
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = toCamelCase(key)
      acc[camelKey] = keysToCamelCase(obj[key])
      return acc
    }, {})
  }
  return obj
}
let sortSingleData=(list_single_data, current_type)=>{
  const languageSkillsSort = ["中文", "英文"];
  const membershipGradeSort = ["SVIP", "VIP"];

  list_single_data.sort((a, b) => {
    const langA = languageSkillsSort.indexOf(a.languageSkills);
    const langB = languageSkillsSort.indexOf(b.languageSkills);
    if (langA !== langB) {
      return langA - langB;
    }
    if (current_type == 4) {
      const gradeA = membershipGradeSort.indexOf(a.membershipGrade);
      const gradeB = membershipGradeSort.indexOf(b.membershipGrade);
      if (gradeA !== gradeB) {
        return gradeA - gradeB;
      }
    } else {
      if (a.price !== b.price) {
        return b.price - a.price;
      }
    }
    return a.recommendDegree - b.recommendDegree;
  });
  return list_single_data;
}

let search_list=(data)=>{
    sound_list_ref.value.close_aduio()
    setTimeout(()=>{
        sound_list_ref.value.list=[]
        sound_list_ref.value.list_loading=true
        // console.log('列表渲染1');
        sound_list_ref.value.init(JSON.parse(JSON.stringify(data)))
    },300)
}
provide('tts',tts);
onMounted(()=>{
    search_ref.value.searchQuery=''
    //包页面返回
    if(route.query.package){
        filter_tag_ref.value.change_title(1) 
        if(route.query.show_detail&&soundStore.showPackage){
            console.log(444);
            
           sound_list_ref.value.go_sound_store_detail(soundStore.showPackage)
          
        } 
    }else if(route.query.buy){
        if( route.query.type=='single'){
          
            
            soundStore.setbuySound({
				type: route.query.type,
				voice_id:route.query.voice_id
			})
        }else{
            soundStore.setbuySound({type:route.query.type})
        }
        filter_tag_ref.value.change_title(soundStore.buy_sound.type=='single'?0:1)  
    }else{
        getFilterTag(4)
    }
   


})
</script>


<style scoped lang="scss">
.soundStore{
    padding-top: var(--gl-headbar-height);;
    background: linear-gradient(180deg, #E7F7EA 0px, rgba(231, 247, 234, 0) 1060px);
    height: fit-content;
    padding-bottom: 0;
    overflow-y: scroll;
    overflow-x: clip; /* 使用clip代替auto，防止下拉显示空白区域 */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    min-height: 100vh; /* 确保至少填满视口高度 */
    display: flex;
    flex-direction: column;
    // min-height: 450vh;
    .soundStore_contanier{
        width: 1162px;
        margin: 0 auto;
        padding-top: 78px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 132px;
    }
    .list_filter{
        display: flex;
        align-items: center;
        align-self: flex-start;
        margin-bottom: 29px;
        line-height: 15px;
        .list_filter_total{
            font-size: 14px;
            color: #7C7C7C;
        }
        .segmentation{
            margin: 0 12px;
            width: 2px;
            height: 15px;
            display: block;
            background-color: #7C7C7C;
        }
        .soundStore_sort{
            margin-left: 4px;
            font-size: 14px;
            margin-right: 2px;
            color: #7C7C7C;
            .soundStore_sort_title{
                margin-right: 17px;
            }
            .soundStore_sort_item{
                margin-right: 16px;
                font-size: 14px;
                color: #271E17;
                cursor: pointer;
                &.current{
                    color: #18AD25;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
        .soundStore_collect{
            margin-left: 3px;
            display: flex;
            align-items: center;
            .el-checkbox-group{
                display: flex;
                align-items: center;
            ::v-deep(.el-checkbox){
                height: 15px;
                line-height: 15px;
                display: flex;
                align-items: center;
            .el-checkbox__input{
                width: 15px;
                height: 15px;
                border: 1px solid #7C7C7C;
                display: flex;
                align-items: center;
                .el-checkbox__inner{
                    height: 100%;
                    border-radius: 0;
                   &:hover{
                    border-color: #18AD25;
                   }
                }
                &.is-checked{
                    border-color:#18AD25 ;
                    .el-checkbox__inner{
                        background-color: #18AD25;
                        border-color: #18AD25;
                        border: none !important;;
                        
                    }
                }
                &:hover{
                    border-color: #18AD25;
                }
            }
            .el-checkbox__label{
                font-size: 14px;
                color: #271E17;
                padding-left: 10px;
                display: flex;
                align-items: center;
                line-height: 15px;
            }
        }
    }
        }
    }
}
</style>
<style>
html,body{
    overflow-y: hidden;
}
</style>