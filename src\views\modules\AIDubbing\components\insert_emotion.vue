<template>
  <div class="insert_emotion" ref="insert_emotion_ref">
    <img src="@/assets/images/aiImages/insert_emotion_arrow.svg" :style="{'left':  + insert_emotion_centerX+'px'}" class="insert_emotion_arrow" alt="">
    <div class="insert_emotion_content">
        <!-- <span class="insert_emotion_content_label">情绪选择</span> -->
        <div class="list_wrapper" :style="{ maxHeight: isCollapsed ? firstRowHeight + 'px' : 'none' }" ref="listRef">
          <div class="insert_emotion_content_list">
              <div class="insert_emotion_content_list_item" v-for="(item,index) in filterArr" :key="index" @click="selectEmotion(item)" :class="currentEmotion==item.id?'current':''">
                  <span class="insert_emotion_content_list_item_default" v-if="item.id==0">
                    通
                  </span>
                  <img class="insert_emotion_content_list_item_img" :src="item.url" alt="" v-else>
                  <span class="insert_emotion_content_list_item_text">{{item.label}}</span>
              </div>
          </div>
        </div>
        <!-- <div class="insert_emotion_opeate" @click="toggleCollapse"  v-if="isMoreThanOneRow" >
          <img src="@/assets/images/aiImages/insert_emotion_expand.png" alt="" v-if="isCollapsed">
          <img src="@/assets/images/aiImages/insert_emotion_retract.svg" alt="" v-else >
        </div> -->
     </div>
  </div>
</template>

<script setup>
import { defineEmits,ref,onMounted, nextTick,defineExpose } from 'vue'
import insert_emotion from '@/assets/images/aiImages/insert_emotion_1.svg'
import insert_emotion1 from '@/assets/images/aiImages/insert_emotion_2.svg'
import insert_emotion2 from '@/assets/images/aiImages/insert_emotion_3.svg'
import insert_emotion3 from '@/assets/images/aiImages/insert_emotion_4.svg'
import insert_emotion4 from '@/assets/images/aiImages/insert_emotion_5.svg'
import insert_emotion5 from '@/assets/images/aiImages/insert_emotion_6.svg'
import insert_emotion6 from '@/assets/images/aiImages/insert_emotion_7.svg'
let emotionList=ref([{
  id:0,
  label:'通用',
  value:'neutral',
  url:''
},{
  id:1,
  label:'高兴',
  value:'happy',
  url:insert_emotion
},{
  id:2,
  label:'悲伤',
  value:'sad',
  url:insert_emotion1
},{
  id:3,
  label:'害怕',
  value:'fearful',
  url:insert_emotion2
},{
  id:4,
  label:'愤怒',
  value:'angry',
  url:insert_emotion3
},{
  id:5,
  label:'厌恶',
  value:'disgusted',
  url:insert_emotion4
},{
  id:6,
  label:'惊讶',
  value:"surprised",
  url:insert_emotion5
}])
let insert_emotion_centerX=ref(0)
let currentEmotion=ref(0)
let isMoreThanOneRow = ref(false)
let filterArr =ref([])//过滤后的数组
let isCollapsed = ref(true)
let insert_emotion_ref=ref(null)
let selectEmotion=(item)=>{
  currentEmotion.value=item.id
  emit('choose',item)
}
let emit = defineEmits(['choose'])
let listRef = ref(null)
let firstRowHeight = ref(0)
let calculateFirstRowHeight=()=>{
 nextTick(() => {
    const listWrapper = listRef.value
    if (!listWrapper) return

    // 找到 insert_emotion_content_list
    const contentList = listWrapper.querySelector('.insert_emotion_content_list')
    if (!contentList) return

    // 找到第一个列表项
    const firstItem = contentList.querySelector('.insert_emotion_content_list_item')
    if (!firstItem) return

    const style = window.getComputedStyle(firstItem)
    const height = firstItem.offsetHeight
    const marginBottom = parseFloat(style.marginBottom) || 0

    firstRowHeight.value = height + marginBottom

    // 判断是否超过一行
    isMoreThanOneRow.value = listWrapper.scrollHeight > firstRowHeight.value

    console.log('firstRowHeight:', firstRowHeight.value, 'scrollHeight:', listWrapper.scrollHeight, 'isMoreThanOneRow:', isMoreThanOneRow.value)
  })
}
let toggleCollapse=()=>{
  isCollapsed.value = !isCollapsed.value
}
let init_emotion=async(data,centerX)=>{
  let rect=''
  let xRelativeToViewport=''
  await nextTick()
   if (insert_emotion_ref.value) {
      rect = insert_emotion_ref.value.getBoundingClientRect();
      xRelativeToViewport = rect.left;
    }
  console.log(data.emotion,'init_emotion');
  // if(data.emotion){
    filterArr.value=filteredEmotionList(data.emotion)
    currentEmotion.value=0
    isCollapsed.value=true
    // xRelativeToViewport
    let xpostion=centerX-xRelativeToViewport
    if(window.innerWidth>1920){
      insert_emotion_centerX.value=xpostion
    }else{
       insert_emotion_centerX.value=xpostion*(1920 / window.innerWidth)
    }
    
    nextTick(() => {
      calculateFirstRowHeight()
    })
  // }
  // // let emotionSettingStr = '[\"happy\", \"sad!\",]'
  // let emotionSettingStr ='[\"happy\", \"sad!\", \"angy\", \"fearful\", \"disgusted\", \"surprised\", \"neutral\"]'
 
}
let  cleanEmotion=(str)=>{
  // 去除非字母字符，转小写
  return str.replace(/[^a-zA-Z]/g, '').toLowerCase()
}
// 解析字符串
let parseEmotionSetting=(str)=>{
  const matches = str.match(/"([^"]*)"/g)
  if (!matches) return []
  return matches.map(s => cleanEmotion(s.slice(1, -1)))
}
// 过滤
let filteredEmotionList = (emotionSettingStr) => {
  const parsedEmotions = parseEmotionSetting(emotionSettingStr)
console.log(parsedEmotions,'parsedEmotions');

  // 先找默认项
  const defaultItem = emotionList.value.find(item => item.value === '')

  // 过滤其他项
  const filteredOthers = emotionList.value.filter(item => {
    if (item.value === '') return false // 默认项先排除，避免重复
    const cleanVal = cleanEmotion(item.value)
    return parsedEmotions.includes(cleanVal)
  })

  // 返回默认项 + 过滤项
  return defaultItem ? [defaultItem, ...filteredOthers] : filteredOthers
}
onMounted(() => {
  calculateFirstRowHeight()
})
defineExpose({
  init_emotion,
})
</script>

<style scoped lang="scss">
.insert_emotion{
    width: 100%;
    margin-top: -10px;
    margin-bottom: 12px;
    display: flex; 
    justify-content: center; 
    position: relative; 
    box-sizing: border-box;
    background:transparent;
    padding-top: 8px;
    .insert_emotion_arrow{
        position: absolute;
        top: 2px;
        z-index: 1;
        width: 18px;
        height: 7px;
    }
    .insert_emotion_content{
        width: 100%;
        background: #F7F7F9;
        border: 0.5px solid #EEF1F3;
        box-sizing: border-box;
        border-radius: 8px;
        border: 0.5px solid #EEF1F3;
        padding: 15px;
        padding-left: 13px;
        padding-bottom: 12px;
        display: flex;
        overflow: hidden;
        .insert_emotion_content_label{
          font-size: 14px;
          line-height: 18px;
          color: #000000;
          margin-right: 15px;
        }
        .list_wrapper{
          flex: 1;
          // width: 494px;
           .insert_emotion_content_list{
              display: flex;
              flex-wrap: wrap;
              row-gap: 12px;
              
              .insert_emotion_content_list_item{
                padding: 0 10px;
                margin-right: 9px;
                // margin-bottom: 12px;
                border: 1px solid #EFEFF1;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 100px;
                line-height: 20px;
                height: 28px;
                box-sizing: border-box;
              .insert_emotion_content_list_item_default{
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  padding: 1px 4px;
                  width: 20px;
                  height: 20px;
                  box-sizing: border-box;
                  background: #FFA02D;
                  border-radius: 100px;
                  font-size: 12px;
                  color: #FFFFFF;
                  margin-right: 6px;
              }
                .insert_emotion_content_list_item_img{
                  width: 21px;
                  margin-right: 6px;
                }
                .insert_emotion_content_list_item_text{
                  font-size: 14px;
                  line-height: 18px;
                  color: #000000;

                }
                &.current{
                  border-color: #0AAF60;
                }
              }
            }
        }
        .insert_emotion_opeate{
          margin-left: auto;
          padding-left: 6px;
          img{
            width: 19px;
            height: 19px;
            cursor: pointer;
          }
        }
    }
}
</style>
