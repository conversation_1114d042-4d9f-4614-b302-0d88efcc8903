{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}, "context7": {"command": "context7-mcp", "args": [], "disabled": false, "autoApprove": []}, "your-mcp-server": {"command": "npx", "args": ["mcp-remote", "http://127.0.0.1:12307/mcp"], "disabled": false, "autoApprove": ["chrome_get_web_content"]}}}