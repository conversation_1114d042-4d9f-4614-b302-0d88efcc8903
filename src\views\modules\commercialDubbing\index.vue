<script setup>
// 定义组件名称，用于keep-alive排除
defineOptions({
  name: 'commercialDubbing'
})

//合成语音接口
import { synthesized_speechApi, Sound_ListApi, Sound_tabs_listApi, filter_sound_listApi, batchCreateAPI,templateList,extractWavByMp3 } from '@/api_my/AlDubb'
import { generateAudiosApi, clip_audio_file_Api, handle_merge_audio_mp3, chekSensitive_Api,batchSave,getListForSave,queryUserUsedVoiceName,queryUserBuyVoiceName,selectVoiceByAI } from '@/api_my/commercialDubb'
import {bookmarkList,bookmarkToggle} from '@/api/soundStore.js'
import {getAlbumsByCreator } from '@/api/mySpace.js'
import jingpin from '@/assets/images/aiImages/jingpin.svg';
import musicIcon from '@/assets/images/aiImages/music.png';
import zhenxiang from '@/assets/images/aiImages/zhenxiang.svg';
import zhizhen from '@/assets/images/commercialImages/zhizhen.png'
import axios from 'axios';
import packageBuyDialog from './components/packageBuyDialog.vue'

import otherStepBg from '@/assets/images/commercialImages/step_bar_bg.png';
import firstStepBg from '@/assets/images/commercialImages/first_step_bar.png';
import other_step_active from '@/assets/images/commercialImages/other_step_active.png';
import first_step_active from '@/assets/images/commercialImages/first_step_active.png';
import contact from'@/views/modules/realVoice/components/index/contact.vue';



import { useAIDubbingStore } from '@/stores/modules/AIDubbing.js'
import { useCommerDubbingStore } from '@/stores/modules/commercialDubbing.js'
import soundEffects from '@/views/constant/musicModal/soundEffects.vue'
import GlobalimportLetter from "@/views/constant/importLetters/importLetter.vue";
import { nanoid } from 'nanoid';
import { pinyin, html, polyphonic } from 'pinyin-pro';
import { ElMessage, ElMessageBox,ElLoading } from 'element-plus'
import { getPinyinWithCharacters } from '@/common/utils/pinyinUtil.js';
import { getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref, watch, watchEffect, computed,onActivated,toRaw ,provide  } from 'vue'
import AudioPlayer from "./components/audioPlayer.vue";
import Index from "@/views/modules/soundStore/index.vue";
import eventBus from "@/common/utils/eventBus.js";
import { useRoute, useRouter } from 'vue-router';
import { useSoundStore } from '@/stores/modules/soundStore.js'
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import aiMatch from './components/aiMatch.vue'
import saveSpaceDialog from './components/saveSpaceDialog.vue'
import { useloginStore } from '@/stores/login'
import collectImage from '@/assets/images/commercialImages/collectImage.svg'
import collectNoImage from '@/assets/images/commercialImages/collectNoImage.svg'
// 引入新增加的商配埋点
import { createCommercialDubbingAnalytics } from '@/utils/umeng/modules/commercialDubbing.js'
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'
import { useAiCopywriting } from '@/views/modules/AIDubbing/hook/useAiCopywriting.js';
import aiCopyWritingCreate from '@/views/modules/AIDubbing/components/aiCopyWritingCreate.vue';
import aiCopyWritingTempalteMore from '@/views/modules/AIDubbing/components/aiCopyWritingTempalteMore.vue';
import daoru from '@/assets/images/aiImages/daoru.svg'
import duoyinzi from '@/assets/images/aiImages/duoyinzi.svg'
import bieming from '@/assets/images/aiImages/bieming.svg'
import charutingdun from '@/assets/images/aiImages/charutingdun.svg'
import shuzifuhao from '@/assets/images/aiImages/shuzifuhao.svg'
import beijingyinle from '@/assets/images/aiImages/beijingyinle.svg'
import yinxiao from '@/assets/images/aiImages/yinxiao.svg'
import pinyin_line from '@/assets/images/aiImages/pinyin_line.svg'
import find_replacement_icon from '@/assets/images/aiImages/find_replacement.svg'
import findReplacement from '@/views/modules/AIDubbing/components/findReplacement.vue';
import { useFindReplace } from '@/views/modules/AIDubbing/hook/useFindReplace.js'
let rate=1
let { userLoading, userError, fetchUserBenefits } = useUserBenefits()
let cancelNavRequest=ref(null)
let loginStore = useloginStore()
// import {crawlTextByMediaFile} from "@/api_my/AlDubb/index.js";
const soundStore = useSoundStore()
const { proxy } = getCurrentInstance();
const useAIDubbing = useAIDubbingStore()
const route = useRoute();
let router = useRouter()
const useCommerDubbing = useCommerDubbingStore()
// 引入umeng
import { useUmeng } from '@/utils/umeng/hook';
// 初始化埋点
const umeng = useUmeng()
const commercialDubbingAnalytics = createCommercialDubbingAnalytics(umeng);
// 下载按钮
// const downlaodButtonArr = reactive([
// 	{ name: '下载音频', url: 'xiazai' },
// 	// {name:'下载视频',url:'shipin1'},
// 	{ name: '下载字幕', url: 'zimu' },
// ])
let package_buy_dialog_ref = ref(null)
// 操作多音字等按钮
const iconsArr = reactive([
	{ IconName: daoru, name: '导入文案' },
	// { IconName: 'duoyinzi', name: '多音字' },
	{ IconName: bieming, name: '读音替换' },
	{ IconName: charutingdun, name: '停顿' },
	{ IconName: shuzifuhao, name: '数字符号' },
	// {IconName:'beijingyinle',name:'背景音乐'},
	// {IconName:'yinxiao',name:'音效'},
	{ IconName: pinyin_line, name: '查看拼音' },
	{ IconName: find_replacement_icon, name: '查找替换' ,className:'find_replacement_popover',needFixed:true,key: 'findReplacement'},
])





// 点击操作多音字等按钮
// const IconNum = ref(0)
const clickIcons = (e, index) => {
	console.log(index);

	closePopUP()
	// const target = event.target.closest('[data-id]');
	// console.log('==========',target)
	// if(!target){
	//   if(target.dataset.id!=='parent-1'){
	//     var div = document.querySelector('div[contenteditable="true"]');
	//     // console.log('77',div)
	//     if (div) { // 检查div是否存在
	//       var children = div.children;
	//       // console.log('ppp',children)
	//       for (var i = 0; i < children.length; i++) {
	//         children[i].classList.remove('tts-tag');
	//       }
	//     }
	//
	//   }

	// }
	showPopover.value = false
	switch (index) {
		case 0:
			// proxy.$letterModal.openImportLetter()
			Letter.value.importDialogVisible = true
			break;
		// case 1:
		// 	// IconNum.value = index
		// 	//多音字操作  点击多音字弹出选择多音字弹窗
		// 	//点击多音字  根据多因去查询这个字的多有多音字 polyphonicList
		// 	gethomograph()
		// 	break;
		case 1:
			// 点击读音替换
			isSelectedAlias()
			break;
		case 2:
			// 停顿弹窗
			stopPopover.value = true
			e.stopPropagation();
			break;
		case 3:
			// 先判断是否是纯数字
			isNanFun()
			break;
		case 4:
			// proxy.$musicmodal.open()
			pinyinFun()
			break;
		case 5:
			//查找替换
			find_replacement()
			// proxy.$effectsmodal.open()
			// console.log(effects.value.effectsDialogVisible)
			// effects.value.effectsDialogVisible = true
			break;
		case 7:
			// 查看拼音
			// pinyinFun()
			break;
	}
}
// div编辑区域调节音量大小
const slideValue = ref(14)
// 中间主体左边文案搜索
const search_copywritingType = ref('')

// 中间主体右边银色搜索
const input_search = ref('')
const search_speaker = () => {
	filter_listFun()
}

// 监听搜索框输入，实现实时搜索
watch(input_search, (newVal) => {
	if (newVal.trim() !== '') {
		search_speaker()
	} else {
		// 如果搜索框为空，则恢复原始音色列表，并根据当前已选的筛选条件过滤
		resetSearch()
	}
})

// 重置搜索，恢复原始音色列表
const resetSearch = () => {
	// 清空搜索框
	input_search.value = ''
	// 恢复音色列表和标签
	filter_listFun()
}

// 搜索框清除按钮的处理函数
const handleClear = () => {
	resetSearch()
}

// 高亮显示匹配的文本
const highlightMatchText = (text, keyword) => {
	if (!keyword || keyword.trim() === '' || !text) {
		return text
	}

	// 防止XSS攻击，对关键词进行转义
	const escapeRegExp = (string) => {
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
	}

	const escapedKeyword = escapeRegExp(keyword.toLowerCase())
	const regex = new RegExp(`(${escapedKeyword})`, 'gi')

	return text.replace(regex, '<span style="color: #0AAF60; font-weight: bold;">$1</span>')
}

// 根据是否有搜索关键词返回平台昵称
const getDisplayName = (item) => {
	if (!input_search.value || input_search.value.trim() === '') {
		return item.platformNickname || ''
	}

	// 检查是否匹配昵称
	if ((item.platformNickname || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		return highlightMatchText(item.platformNickname || '', input_search.value)
	}

	// 如果昵称不匹配，正常显示
	return item.platformNickname || ''
}

// 根据是否有搜索关键词返回情感标签
const getDisplayEmotionTag = (item) => {
	if (!input_search.value || input_search.value.trim() === '') {
		return item.emotionTags || ''
	}

	// 检查是否匹配情感标签
	if ((item.emotionTags || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		return highlightMatchText(item.emotionTags || '', input_search.value)
	}

	// 检查是否匹配场景分类
	if ((item.sceneCategory || '').toLowerCase().includes(input_search.value.toLowerCase())) {
		// 如果匹配场景分类，添加场景分类信息到标签
		return `${item.emotionTags || ''} <span style="color: #0AAF60; font-weight: bold;">[${item.sceneCategory}]</span>`
	}

	// 如果都不匹配，正常显示
	return item.emotionTags || ''
}

// 中间主体右边音色列表滚动事件
const scroll = ref(null);
const handleScroll = (e) => {
	const wrapRef = scroll.value.wrapRef;
	// console.log('wrapRef.scrollHeight',wrapRef.scrollHeight)   //内容总高度560
	// console.log('wrapRef.clientHeight',wrapRef.clientHeight)    //可视区域高度300
	// console.log('event.scrollTop',e.scrollTop)    //// 滚动条距盒子顶部高度260
	let poor = wrapRef.scrollHeight - wrapRef.clientHeight;
	// 判断滚动到底部
	if (e.scrollTop + 20 >= poor) {
		console.log('ppppppp到底了')
	}
}

// 中间主体右边语速滑块
const speechValue = ref(1)
// 语调
const intonationValue = ref(0)


const progress_barNum3_speechValue = ref(1)
const progress_barNum3_intonationValue = ref(0)


// 是否播放音乐
const isPauseTtsAudio = ref(false)
// 点击文案类型中更多按钮
const clickMoreIcon = () => {
	proxy.$AImodal.open()
}
// 输入
const MAX_LENGTH = 5000
const textLength = ref(0);
const editorRef = ref(null)
const isComposing = ref(false)
// 输入的文本内容
const editorContent = ref('')
let aiPostion=ref('')
// // 输入事件处理
const handleInput = async(event) => {
	let scrollbarEl = document.querySelector('.speaker_content_bottom_left');
	const scrollbarWrap = document.querySelector('.el-scrollbar__wrap');
	hasImg.value = editorRef.value.textContent.length>0
	const content = ref('');
	const text = event.target.innerText || event.target.textContent;
	if (text.length >= MAX_LENGTH) {
		// 截断内容到最大长度
		event.target.innerText = text.substring(0, MAX_LENGTH);
		// // 设置光标到内容末尾
		// const range = document.createRange();
		// const sel = window.getSelection();
		// range.setStart(event.target.firstChild, MAX_LENGTH);
		// range.collapse(true);
		// sel.removeAllRanges();
		// sel.addRange(range);
	} else {
		// 更新内容（虽然这里内容没有变化，但为了确保响应式，可以保留此行代码）
		// content.value = text;
	}
	editorContent.value = event.target.textContent
	// 设置底部文本长度
	textLength.value = event.target.textContent.length
	let editor = document.getElementById('editor');
	if (editor) {
		let rect = editor.getBoundingClientRect();
		console.log('editor', rect);
		// aiPostion.value = rect.bottom-(260*(window.innerHeight/953))
		// if (scrollbarEl) {
		// 	const height = scrollbarEl.offsetHeight;
		// 	if(aiPostion.value>height){
		// 		aiPostion.value = height-(120*(window.innerHeight/953))
		// 	}
		// }
		let height1=(rect.bottom/(window.innerHeight/953))-(25/(window.innerHeight/953))

		if (scrollbarEl) {


			// let max_height=(height/(window.innerHeight/953))-(40/(window.innerHeight/953))
			const height = scrollbarEl.getBoundingClientRect().bottom
			let height1=0
			let max_height=0
			if(window.innerHeight>953){
				height1=rect.bottom-25
				max_height=height-80
			}else{
				height1=(rect.bottom/(window.innerHeight/953))-(25/(window.innerHeight/953))
				max_height=(height/(window.innerWidth/1920))-80
			}
			console.log('height',aiPostion.value,max_height);
			await nextTick();
			setTimeout(() => {
				if (editorRef.value) {
					const scrollbarView = editorRef.value.closest('.el-scrollbar__view');
					if (scrollbarView) {
						scrollbarView.scrollTop = scrollbarView.scrollHeight
					} else {
						editorRef.value.scrollTop = editorRef.value.scrollHeight
					}
				}
			}, 200);
			if(height1>max_height){
				console.log('超出');
			aiPostion.value = max_height
			}else{
				aiPostion.value = height1
			}
			// if(rate<1){
			// 	aiPostion.value=aiPostion.value/rate
			// }
			if(height1>max_height-90){
				copywriting_aiPostion.value=max_height-90
			}else{
				copywriting_aiPostion.value=aiPostion.value
			}
		}
		console.log(scrollbarWrap.offsetHeight,aiPostion.value,'滚动高度');
	}
}
let copywriting_aiPostion=ref(0)
// 禁止方向键、删除键和退格键（仅作为示例，可能需要根据实际需求调整）
// const preventDefault = (event) => {
//   event.preventDefault();
// }

// 查看拼音按钮
const pinyinBool = ref(false)
const pinyinResult = reactive([])
const pinyinFun = () => {
	// const aa = pinyin(editorContent.value,{ type: 'array' })
	pinyinBool.value = !pinyinBool.value
	if (pinyinBool) {
		const result = getPinyinWithCharacters(editorContent.value);
		pinyinResult.splice(0, pinyinResult.length, ...result);
	}
}
// 鼠标按下事件
const handleKeyUp = (e) => {
	if (e.keyCode == 37 || e.keyCode == 38 || e.keyCode == 39 || e.keyCode == 40) {
		// mouseup()
	}
}
//  多音字弹窗
const showPopover = ref(false)
// 多音字列表
const polyphonicList = ref([])
// 点击多音字时调用的函数

// 判断选区后面是否有div的方法
function isDivAfterRange(range) {
	const { endContainer, endOffset } = range;
	let node = endContainer;
	let offset = endOffset;

	// 情况1：选区结束在文本节点中间
	if (node.nodeType === Node.TEXT_NODE) {
		if (offset < node.length) return false; // 文本节点内不可能包含div
		node = node.parentNode;
		offset = Array.from(node.childNodes).indexOf(endContainer) + 1;
	}

	// 情况2：处理元素节点
	let currentNode = node;
	let nextIndex = offset;

	// 创建遍历器
	const walker = document.createTreeWalker(
		document.body,
		NodeFilter.SHOW_ELEMENT,
		{
			acceptNode: n =>
				n.tagName === 'DIV' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
		}
	);

	// 定位到当前节点位置
	walker.currentNode = currentNode;

	// 检查后续兄弟节点
	if (currentNode.childNodes[nextIndex]) {
		const nextNode = currentNode.childNodes[nextIndex];
		if (nextNode.tagName === 'DIV') return true;
	}

	// 遍历后续节点
	while (walker.nextSibling()) {
		if (walker.currentNode.tagName === 'DIV') return true;
	}

	// 深度遍历父节点链
	// while (currentNode.parentNode) {
	//   currentNode = currentNode.parentNode;
	//   const children = Array.from(currentNode.childNodes);
	//   const index = children.indexOf(node) + 1;
	//
	//   if (children.slice(index).some(n => n.tagName === 'DIV')) {
	//     return true;
	//   }
	//   node = currentNode;
	// }

	return false;
}




const gethomograph = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('ppp',range.commonAncestorContainer.nextElementSibling)
	// if(range.commonAncestorContainer.nextElementSibling){
	//   showPopover.value = true
	//   return
	// }
	// return
	if (range.toString().length !== 1) {
		ElMessage({
			message: '请滑选单个汉字',
			type: 'warning',
		})
		return
	}
	// const resultString = polyphonic(range.toString(),{ type: 'array' });
	const resultString = polyphonic(range.toString(), { toneType: 'num', type: 'array' });
	console.log('4545', resultString); // 输出
	if (resultString[0].length > 0) {
		polyphonicList.value = []
		polyphonicList.value = resultString[0]
		setTimeout(() => {
			showPopover.value = true
		})
	}
}
// 点击多音字中的某个读音，更新多音字读音在页面上
const clickPolyphonic = (item) => {
	restoreSelection()
	getSelectedText(item)
	showPopover.value = false
}




// 创建唯一标识数组  并保存起来  当点击读音替换还是数字符号的时候保存起来，后面替换内容会用到
const createUniqueArr = []
// 记录选中的读音替换以及多音字     以及相对应替换的文字，  以键值对形式
// 保存选择的多音字数组列表
const selectedTextList = reactive([])

// // 多音字操作
// editorRef  //  输入文本的ref
const getSelectedText = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}

	const hasTextAfter = isDivAfterRange(range);
	console.log('llllllll', hasTextAfter)
	if (hasTextAfter) {
		console.log(range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side'))
		range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
		let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
		selectedTextList.map(child => {
			if (child.id == dataId) {
				child.name = `${range.toString()}/(${item})`
			}
		})
		return
	}
	// if(range.commonAncestorContainer.nextElementSibling){
	//   console.log('lll777',range.commonAncestorContainer.nextElementSibling.firstElementChild)
	//   if(range.commonAncestorContainer.nextElementSibling.firstElementChild){
	//     range.commonAncestorContainer.nextElementSibling.firstElementChild.innerHTML = item
	//     let dataId = range.commonAncestorContainer.nextElementSibling.firstElementChild.getAttribute('data-side')
	//     selectedTextList.map(child=>{
	//       if(child.id==dataId){
	//         child.name = `${range.toString()}/(${item})`
	//       }
	//     })
	//     return
	//   }
	//   return
	// }

	let id = nanoid(4)
	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span data-side='${id}'>${item}</span> <button style='font-size:10px; margin-left:5px;'>×</button>`;
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.cursor = "pointer";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	// {word:range.toString(),replacement:item}

	selectedTextList.push({
		id: id,
		name: `${range.toString()}/(${item})`
	})

	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// console.log(e.target.parentElement.firstElementChild.getAttribute('data-side'))
		let click_id = e.target.parentElement.firstElementChild.getAttribute('data-side')

		selectedTextList.map((child, index) => {
			if (child.id == click_id) {
				selectedTextList.splice(index, 1)
			}
		})


		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
			// showPopover.value = true
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// range.deleteContents()
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 读音替换弹窗
const aliasPopover = ref(false)
const aliasValue = ref('')  //读音替换input
// 判断有没有选择文字
const isSelectedAlias = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll',range.toString().length)
	if (range.toString().length === 0) {
		ElMessage({
			message: '请至少选一个汉字',
			type: 'warning',
		})
		return
	}
	aliasPopover.value = true

}

// 点击弹窗中添加读音替换按钮
const addAlias = () => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		})
		return "";
	}
	var sel = window.getSelection();
	var selectedText = sel.toString().trim();
	if (!selectedText) {
		ElMessage({
			message: '请先选中内容',
			type: 'warning',
		})
		return "";
	}
	var tipBox = document.createElement("div");
	let id = nanoid(4)
	tipBox.innerHTML = "<span class='change'>" + aliasValue.value + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "alias");
	tipBox.setAttribute("data-key", selectedText);
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid purple";
	tipBox.style.backgroundColor = "#f0e0ff";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";

	createUniqueArr.push(id)



	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', aliasValue.value);



	// range.deleteContents();
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、

		// var key = tipBox.getAttribute("data-key");
		// if (window.aliasMapping && key in window.aliasMapping) {
		//   delete window.aliasMapping[key];
		// }
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "alias") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		alert("未检测到选中文本");
	}
	// if (!window.aliasMapping) {
	//   window.aliasMapping = {};
	// }

	aliasPopover.value = false
	aliasValue.value = ''
}



// 遍历指定div下所有节点、查找指定类名并替换文本
// function replaceDivText(containerId, targetClass, newText) {
//   // 获取容器div元素‌:ml-citation{ref="1,5" data="citationList"}
//   const container = document.getElementById(containerId);
//   if (!container) return null;
//
//   // 递归遍历所有子节点‌:ml-citation{ref="4,5" data="citationList"}
//   const traverseNodes = (node) => {
//     if (node.nodeType === Node.ELEMENT_NODE) {
//       // 检查当前节点是否包含目标类名‌:ml-citation{ref="1,4" data="citationList"}
//       if (node.classList.contains(targetClass)) {
//         node.textContent = newText; // 替换文本内容‌:ml-citation{ref="3,6" data="citationList"}
//       }
//       // 继续遍历子节点‌:ml-citation{ref="4,5" data="citationList"}
//       Array.from(node.children).forEach(child => traverseNodes(child));
//     }
//   };
//
//   traverseNodes(container);
//   return container.innerHTML; // 返回处理后的HTML内容‌:ml-citation{ref="1,6" data="citationList"}
// }

// 1. 定义一个模块级变量，保证组件多次挂载时共享状态
let hasRun = false
/**
 * 克隆容器DOM结构并执行安全替换
 * @param {string} containerId 容器元素ID
 * @param {string} targetClass 目标类名
 * @param {string} newText 新文本内容
 * @returns {string} 处理后的HTML字符串
 */
// 示例调用   获取数字替换以及读音替换替换之后的文本内容 （编辑的所有内容）
let the_init=()=>{
	console.log('AI商配页面 onMounted 触发 - 每次进入都会执行');

	close_music_div()

	// get_Sound_tabs_list() // 移除这个调用，使用 soundListFun 中的分类提取逻辑
	window.addEventListener('click', handleClickOutside);
	window.addEventListener('mousedown', globalClickHandler)
	slider_input_init()
	audioRefEnd()
	// window.addEventListener('click', handleClickOutside1);

	// 原 onActivated 的逻辑
	change_list_nav(0)
	full_loading_timer = setInterval(() => {
		full_loading_active_index.value = (full_loading_active_index.value + 1) % 6;
	}, 300); // 300ms切换一次，和动画节奏对应
	init_aiPostion()
	let editor = editorRef.value;
	if (!editor) return;

	// 先解绑，防止重复绑定
	editor.removeEventListener('dragover', handleDragOver);
	editor.removeEventListener('drop', handleDrop);

	editor.addEventListener('dragover', handleDragOver);
	editor.addEventListener('drop', handleDrop);
}

let doSomething=()=>{
	
  if (hasRun) return
    console.log('hasRun:', hasRun);
  the_init()
  hasRun = true
  rate=getBodyScale()

}
onMounted(() => {
	console.log('onMounted');
	
	doSomething()
	hasRun = true
})
let getBodyScale=()=>{
  const el = document.querySelector('#app');
  if (!el) return 1;
  const transform = getComputedStyle(el).transform;
  if (!transform || transform === 'none') return 1;
  const values = transform.match(/matrix\((.+)\)/)[1].split(', ');
  return parseFloat(values[0]); // scale
}

let handleDragOver=(e)=>{
  e.preventDefault();
}
let handleDrop=(e)=>{
  e.preventDefault();
   if (isInserting) return; // 防止重复触发
  isInserting = true;

  const dataTransfer = e.dataTransfer;

  if (dataTransfer.files && dataTransfer.files.length > 0) {
    for (let i = 0; i < dataTransfer.files.length; i++) {
      const file = dataTransfer.files[i];
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(event) {
          insertImageAtCursor(event.target.result);
          isInserting = false; // 插入完成，释放锁
        };
        reader.readAsDataURL(file);
        break; // 只插入第一张图片
      }
    }
  } else {
    const htmlData = dataTransfer.getData('text/html');
    if (htmlData) {
      const div = document.createElement('div');
      div.innerHTML = htmlData;
      const img = div.querySelector('img');
      if (img) {
        insertImageAtCursor(img.src);
      } else {
        const text = dataTransfer.getData('text/plain');
        insertTextAtCursor(text);
      }
    } else {
      const text = dataTransfer.getData('text/plain');
      insertTextAtCursor(text);
    }
    isInserting = false; // 释放锁
  }
  

}



let init_aiPostion=async()=>{
	await nextTick()
	setTimeout(()=>{
	let editor = document.getElementById('editor');
	let rect = editor.getBoundingClientRect();
	aiPostion.value =(rect.bottom/(window.innerHeight/953))-(25/(window.innerHeight/953))
	// if(rate<1){
	// 	aiPostion.value=aiPostion.value/rate
	// }
	copywriting_aiPostion.value=aiPostion.value
	},400)

}
onUnmounted(() => {
	window.removeEventListener('click', handleClickOutside);
	window.removeEventListener('mousedown', globalClickHandler)
	// window.removeEventListener('click', handleClickOutside1);
	audioRefEnd()
	clearInterval(full_loading_timer);
});
// 音色标签列表
// 精品和珍享数据列表
const voiceTypeArr = ref([{ name: '全部', selected: false }])
// const gendersArr = ref([{name:'sex',children:[{name:'全部',selected:false}]}])
const gendersArr = ref([{ name: '全部', selected: false }])
// const secondArr = ref([{name:'scene',children:[{name:'全部',selected:false}]}])
const secondArr = ref([{ name: '全部', selected: false }])
// const thirdArr = ref([{name:'three',children:[{name:'全部',selected:false}]}])
const thirdArr = ref([{ name: '全部', selected: false }])
let emotionTagsArr = ref([{ name: '全部', selected: false }])
// 第三层去重后的数组
const uniqueList = ref()
// 第三层需要展示的数据
const thirdArrList = ref()
//我的音色展示的数据
let myArrList = ref([
	{
		name: '收藏', selected: false, visible: true, level: 0,
	},{
		name: '已购', selected: false, visible: true, level: 0,
	},{
		name: '历史', selected: false, visible: true, level: 0,
	}
])
// 新增变量，用于存储所有音色数据，避免重复请求
const allSoundList = ref([])

// 添加变量存储初始分类数据
const initialCategories = {
  voiceTypes: ref([]),
  genders: ref([]),
  sceneCategories: ref([]),
  recommendTags: ref([]),
  emotionTags: ref([])
}

const selecteVoiceTypeNum = ref('全部')
const selecteVoiceType = (item) => {
	selecteVoiceTypeNum.value = item.name
	// 重置所有下级分类的选中值
	selecteGenderNum.value = '全部'
	selecteSecondNum.value = '全部'
	selecteUniqueNum.value = '全部'
	selecteEmotionNum.value = '全部'

	// 更新下级分类的可选项
	updateCategoryOptions()

	// 应用筛选
	filter_listFun()
}

const selecteGenderNum = ref('全部')
const selecteGender = (item) => {
	selecteGenderNum.value = item.name

	// 重置所有下级分类的选中值
	selecteSecondNum.value = '全部'
	selecteUniqueNum.value = '全部'
	selecteEmotionNum.value = '全部'

	// 更新下级分类的可选项
	updateCategoryOptions()

	// 应用筛选
	filter_listFun()
}

const selecteSecondNum = ref('全部')
const selecteSecond = (item, index) => {
	selecteSecondNum.value = item.name

	// 重置下级分类的选中值
	selecteUniqueNum.value = '全部'
	selecteEmotionNum.value = '全部'

	// 更新下级分类的可选项
	updateCategoryOptions()

	// 应用筛选
	filter_listFun()
}

const selecteUniqueNum = ref('全部')
let selecteEmotionNum = ref('全部')
const selecteUnique = (item) => {
	selecteUniqueNum.value = item.name

	// 重置下级分类的选中值（如果有的话）
	selecteEmotionNum.value = '全部'

	// 更新下级分类的可选项（如果有的话）
	updateCategoryOptions()

	// 应用筛选
	filter_listFun()
}

let selecteEmotion = (item) => {
	selecteEmotionNum.value = item.name

	// 应用筛选
	filter_listFun()
}

// 新增函数：根据当前选中的分类更新下级分类的可选项
const updateCategoryOptions = () => {
	// 如果没有数据，不执行任何操作
	if (!allSoundList.value || allSoundList.value.length === 0) return

	// 根据当前已选择的筛选条件，筛选出符合条件的音色列表
	let filteredItems = allSoundList.value.filter(item => {
		// 检查性别和年龄条件
		if (selecteGenderNum.value !== '全部') {
			if (!item.gender || !item.gender.includes(selecteGenderNum.value)) {
				return false
			}
		}

		// 检查场景分类条件
		if (selecteSecondNum.value !== '全部' && item.sceneCategory !== selecteSecondNum.value) {
			return false
		}

		// 检查推荐标签条件
		if (selecteUniqueNum.value !== '全部') {
			let tags = []
			if (typeof item.recommendTags === 'string') {
				if (item.recommendTags.includes(',')) {
					tags = item.recommendTags.split(',')
				} else if (item.recommendTags.includes('、')) {
					tags = item.recommendTags.split('、')
				} else {
					tags = [item.recommendTags]
				}
			} else if (Array.isArray(item.recommendTags)) {
				tags = item.recommendTags
			} else {
				tags = [item.recommendTags]
			}

			if (!tags.some(tag => tag.trim() === selecteUniqueNum.value || tag.trim().includes(selecteUniqueNum.value))) {
				return false
			}
		}

		return true
	})

	// 不管是选择哪一级分类，前三级分类都保持不变，只更新第四级分类（情感标签）
	// 确保前三级分类使用初始值
	gendersArr.value = [...initialCategories.genders.value]
	secondArr.value = [...initialCategories.sceneCategories.value]
	thirdArrList.value = [...initialCategories.recommendTags.value]
	// 只更新第四级情感标签
	updateRecommendOptions(filteredItems)
}

// 更新二级、三级和四级分类
const updateLowerCategories = (filteredItems) => {
	// 不再需要更新二级分类，直接使用初始数据
	// updateGenderOptions(filteredItems)
	updateSceneAndRecommendOptions(filteredItems)
}

// 更新三级和四级分类
const updateSceneAndRecommendOptions = (filteredItems) => {
	// 不再需要更新三级分类，直接使用初始数据
	// updateSceneOptions(filteredItems)
	updateRecommendOptions(filteredItems)
}

// 仅保留供初始化使用
const updateGenderOptions = (allData) => {
	// 保留"全部"选项
	gendersArr.value = [{ name: '全部', selected: false }]

	// 从筛选后的列表中提取唯一的性别/年龄值
	const genders = new Set()

	allData.forEach(item => {
		if (item.gender) {
			const genderParts = item.gender.split('、')
			genderParts.forEach(part => genders.add(part.trim()))
		}
	})

	// 将提取的值添加到选项数组
	genders.forEach(gender => {
		gendersArr.value.push({
			name: gender,
			selected: false
		})
	})

	// 保存初始数据
	initialCategories.genders.value = [...gendersArr.value]
}

// 仅保留供初始化使用
const updateSceneOptions = (allData) => {
	// 保留"全部"选项
	secondArr.value = [{ name: '全部', selected: false }]

	// 从筛选后的列表中提取唯一的场景分类值
	const sceneCategories = new Set()

	allData.forEach(item => {
		if (item.sceneCategory) {
			sceneCategories.add(item.sceneCategory)
		}
	})

	// 将提取的值添加到选项数组
	sceneCategories.forEach(category => {
		secondArr.value.push({
			name: category,
			selected: false
		})
	})

	// 保存初始数据
	initialCategories.sceneCategories.value = [...secondArr.value]
}

// 更新推荐标签选项（四级分类）
const updateRecommendOptions = (filteredItems) => {
	// 使用原始情感标签数据，而不是创建新的
	emotionTagsArr.value = [...initialCategories.emotionTags.value]

	// 如果没有选择前三级分类，则不需要筛选情感标签
	if (selecteGenderNum.value === '全部' && selecteSecondNum.value === '全部' && selecteUniqueNum.value === '全部') {
		return
	}

	// 从筛选后的列表中提取唯一的情感标签值
	const emotionTagsSet = new Set()

	filteredItems.forEach(item => {
		if (item.emotionTags) {
			// 处理可能的多值情况，先检查是否包含逗号，再检查是否包含顿号
			let tags = []
			if (typeof item.emotionTags === 'string') {
				if (item.emotionTags.includes(',')) {
					tags = item.emotionTags.split(',')
				} else if (item.emotionTags.includes('、')) {
					tags = item.emotionTags.split('、')
				} else {
					tags = [item.emotionTags]
				}
			} else if (Array.isArray(item.emotionTags)) {
				tags = item.emotionTags
			} else {
				tags = [item.emotionTags]
			}

			tags.forEach(tag => {
				if (tag && tag.trim() !== '') {
					emotionTagsSet.add(tag.trim())
				}
			})
		}
	})

	// 过滤emotionTagsArr保留"全部"选项和在filteredItems中找到的选项
	emotionTagsArr.value = emotionTagsArr.value.filter(item => {
		return item.name === '全部' || emotionTagsSet.has(item.name)
	})
}

// 更新情感标签选项
const updateEmotionOptions = (filteredItems) => {
	// 保留"全部"选项
	emotionTagsArr.value = [{ name: '全部', selected: false }]

	// 从筛选后的列表中提取唯一的情感标签值
	const emotionTags = new Set()

	filteredItems.forEach(item => {
		if (item.emotionTags) {
			// 处理可能的多值情况
			const tags = typeof item.emotionTags === 'string'
				? item.emotionTags.split('、')
				: Array.isArray(item.emotionTags)
					? item.emotionTags
					: [item.emotionTags]

			tags.forEach(tag => {
				if (tag && tag.trim() !== '') {
					emotionTags.add(tag.trim())
				}
			})
		}
	})

	// 将提取的值添加到选项数组
	emotionTags.forEach(tag => {
		emotionTagsArr.value.push({
			name: tag,
			selected: false
		})
	})
}

// 根据选中条件过滤音色列表
const filter_listLoading = ref(false)
const filter_listFun = () => {
	console.log('filter_listFun');

	filter_listLoading.value = true

	try {
		// 根据当前筛选条件过滤音色列表
		let filteredList = allSoundList.value.filter(item => {
			// 第1级：性别/年龄 - gender字段
			if (selecteGenderNum.value !== '全部') {
				// 处理gender可能是多值的情况
				if (!item.gender) return false

				const genderInfo = item.gender.split('、')
				if (!genderInfo.includes(selecteGenderNum.value)) {
					return false
				}
			}

			// 第3级：场景分类 - sceneCategory字段
			if (selecteSecondNum.value !== '全部' && item.sceneCategory !== selecteSecondNum.value) {
				return false
			}

			// 第4级：推荐标签 - recommendTags字段
			if (selecteUniqueNum.value !== '全部') {
				// 处理recommendTags可能是数组或字符串的情况
				let recommendTags = []
				if (typeof item.recommendTags === 'string') {
					if (item.recommendTags.includes(',')) {
						recommendTags = item.recommendTags.split(',')
					} else if (item.recommendTags.includes('、')) {
						recommendTags = item.recommendTags.split('、')
					} else {
						recommendTags = [item.recommendTags]
					}
				} else if (Array.isArray(item.recommendTags)) {
					recommendTags = item.recommendTags
				} else {
					recommendTags = [item.recommendTags]
				}

				if (!recommendTags.some(tag => tag.trim() === selecteUniqueNum.value || tag.trim().includes(selecteUniqueNum.value))) {
					return false
				}
			}

			// 第5级：情感标签 - emotionTags字段
			if (selecteEmotionNum.value !== '全部') {
				// 处理emotionTags可能是数组或字符串的情况
				let emotionTags = []
				if (typeof item.emotionTags === 'string') {
					if (item.emotionTags.includes(',')) {
						emotionTags = item.emotionTags.split(',')
					} else if (item.emotionTags.includes('、')) {
						emotionTags = item.emotionTags.split('、')
					} else {
						emotionTags = [item.emotionTags]
					}
				} else if (Array.isArray(item.emotionTags)) {
					emotionTags = item.emotionTags
				} else {
					emotionTags = [item.emotionTags]
				}

				if (!emotionTags.some(tag => tag.trim() === selecteEmotionNum.value || tag.trim().includes(selecteEmotionNum.value))) {
					return false
				}
			}

			// 检查搜索关键词 - platformNickname字段
			if (input_search.value && input_search.value.trim() !== '') {
				const keyword = input_search.value.toLowerCase()
				const platformNickname = (item.platformNickname || '').toLowerCase()
				const emotionTags = (item.emotionTags || '').toLowerCase()
				const sceneCategory = (item.sceneCategory || '').toLowerCase()

				// 匹配名称、情感标签或场景分类
				if (!platformNickname.includes(keyword) &&
					!emotionTags.includes(keyword) &&
					!sceneCategory.includes(keyword)) {
					return false
				}
			}

			return true
		})

		// 检查是否所有分类都选择"全部"且存在搜索关键词
		const isAllCategoriesSelected = () => {
			return selecteGenderNum.value === '全部' &&
				   selecteSecondNum.value === '全部' &&
				   selecteUniqueNum.value === '全部' &&
				   selecteEmotionNum.value === '全部';
		};

		// 检查是否完全匹配搜索关键词
		const isExactMatch = (item, keyword) => {
			const platformNickname = (item.platformNickname || '').toLowerCase();
			return platformNickname === keyword.toLowerCase();
		};

		// 特殊排序：当所有分类都选择"全部"且有搜索关键词时，完全匹配排在前面
		if (isAllCategoriesSelected() && input_search.value && input_search.value.trim() !== '') {
			const keyword = input_search.value.trim();

			// 分为完全匹配和模糊匹配两组
			const exactMatches = filteredList.filter(item => isExactMatch(item, keyword));
			const partialMatches = filteredList.filter(item => !isExactMatch(item, keyword));

			// 对两组分别应用会员等级排序
			const sortedExactMatches = sortVoicesByMembership(exactMatches);
			const sortedPartialMatches = sortVoicesByMembership(partialMatches);

			// 完全匹配排在前面，模糊匹配排在后面
			soundList.value = [...sortedExactMatches, ...sortedPartialMatches];
		} else {
			// 使用会员等级排序逻辑
			soundList.value = sortVoicesByMembership(filteredList);
		}

		// 设置音色列表项的初始状态
		soundList.value.forEach(item => {
			// 保持已选状态，只设置未设置过的项
			if (item.isPlay === undefined) item.isPlay = false
			if (item.isSelected === undefined) item.isSelected = false
		})

		filter_listLoading.value = false
	} catch (error) {
		console.error('筛选音色列表出错：', error)
		filter_listLoading.value = false
		soundList.value = []
	}
}




const thirdOriginArr = ref([])
const get_Sound_tabs_list = () => {
	Sound_tabs_listApi({ tts: '6' }).then(res => {
		// console.log('456',res)
		if (res.code == 0) {
			// console.log('798798',res.data)
			let { data } = res
			let { genders, sceneMetadata, grade, emotionTags } = data
			// genders
			// 精品和珍享数组
			grade.map((item, index) => {
				voiceTypeArr.value.push({
					name: item.grade,
					selected: false,
				})
			})
			// 第一层数组
			genders.map((item, index) => {
				gendersArr.value.push({
					name: item.gender,
					selected: false,
				})
			})
			emotionTags.map((item, index) => {
				emotionTagsArr.value.push({
					name: item.emotion_tag,
					selected: false,
				})
			})
			// 这里不需要传统商配标签,后端已处理
			// sceneMetadata.shift()
			thirdOriginArr.value = sceneMetadata
			// Object.assign(thirdOriginArr, JSON.parse(JSON.stringify(sceneMetadata)))
			thirdOriginArr.value.map(item => {
				item.recommend_tags.map(child => {
					child.selected = false
				})
			})
			// console.log('originArr',thirdOriginArr.value)
			// 第二层数组
			sceneMetadata.map(item => {
				item.recommend_tags.map(child => {
					// 第三层数组
					thirdArr.value.push({ name: child.recommend_tags, selected: false })
				})
				// 第二层数组
				secondArr.value.push({ name: item.scene_category, selected: false })
			})

			// console.log('ooo',thirdArr.value)
			uniqueList.value = thirdArr.value.filter(
				(item, index) => thirdArr.value.findIndex(i => i.name === item.name) === index
			);
			// console.log('uniqueList',uniqueList)
			thirdArrList.value = uniqueList.value


		} else {

		}
	})
}


// 查询音色列表函数
// 音色列表顶部筛列表


const categories = ref([])




// 点击列表中某一项
let SoundItemId = ref('')
const SoundItem = ref({})
const selectSoundItem = (item) => {
	console.log('///////////', item, selected_timbre_filter_arr.value.length)
	// 保存单个选中
	selected_timbre_filter_arr.value = []
	selected_timbre_filter_arr.value.length = 0
	SoundItem.value = item
	selected_timbre_arr.value = [item]
	console.log('78888888', selected_timbre_arr.value)
	console.log('selected_timbre_arr', selected_timbre_arr)
	selected_timbre_arr.value.map(item => {
		selected_timbre_filter_arr.value.push(item.voiceName)
	})
console.log(selected_timbre_filter_arr.value,selected_timbre_arr,sampleValue.value,'选的啥');

	// sampleValue   判断是不是多人出样，如果是，则是多选，否则就是单选
	if (sampleValue.value) {
		SoundItemId.value = item.voiceName
		//恢复语速和语调默认值
		speechValue.value = 1
		//语调
		intonationValue.value = 0
		soundList.value.map(item => {
			if (SoundItemId.value == item.voiceName) {
				item.isSelected = true
			} else {
				item.isSelected = false
			}
		})
	} else {
		const selectedItems = soundList.value.filter(item => item.isSelected)
		if (selectedItems.length >= 5 && !item.isSelected) {
			ElMessage({
				message: '最多只能选5个音色',
				type: 'warning',
			});
			return;
		}
		item.isSelected = !item.isSelected;
	}








}
let soundList = ref([])
const originalSoundList = ref([])
const soundListFun = () => {
	cancelNavRequest.value = axios.CancelToken.source();
	return new Promise((resolve, reject) => {
	Sound_ListApi({ tts: '5',userId:JSON.parse(localStorage.getItem('user'))?.userId || '',cancelToken: cancelNavRequest.value.token }).then(res => {
		console.log('456',res)
		originalSoundList.value.filter(item =>
			item.name&&item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
		)
		if (res.code == 0) {
			// 调试打印
			console.log('音色列表数据样例：', res.data.slice(0, 5));
			// 专门检查头像URL
			res.data.forEach(item => {
				if (item.avatarUrl) {
					console.log(`音色 ${item.platformNickname || item.voiceName} 的头像URL: ${item.avatarUrl}`);
				} else {
					console.log(`音色 ${item.platformNickname || item.voiceName} 没有avatarUrl字段!`);
				}
			});

			if (res.data.length > 0) {
				//声音商店包跳转没有的设置禁选
				res.data.map((item) => {
					item.isPlay = false
					item.isSelected = false
				})
				resolve(true)
			}
			// 存储完整的音色列表数据
			allSoundList.value = JSON.parse(JSON.stringify(res.data))
			Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res.data)))

			// 应用排序逻辑
			let processedData = sortVoicesByMembership(JSON.parse(JSON.stringify(res.data)))
			Object.assign(soundList.value, processedData)

			getSpaceSave()//获取我的空间跳转保存内容
			// 从音色列表数据中提取分类信息
			extractCategories(res.data)

			if (route.query.choose && soundStore.chooseData) {
				let index = soundList.value.findIndex((item) => item.id == soundStore.chooseData.id)
				if (index >= 0) {
					SoundItemId.value = soundList.value[index].voiceName
					selectSoundItem(soundList.value[index])
					scrollToElement()
					soundStore.setChooseData(null)
				} else {
					ElMessage({ message: '未在ai商配中找到该音色!', type: 'warning' });
				}
			}
			// console.log('ppp',soundList)
		} else {
			soundList.value = []
			originalSoundList.value = []
		}
	}).catch(err => {
		console.log(err)
		soundList.value = []
		originalSoundList.value = []
	})
})
}

// 新增函数：从音色列表数据中提取分类信息
const extractCategories = (data) => {
	if (!data || data.length === 0) return

	// 清空现有分类数据数组，但保留"全部"选项
	gendersArr.value = [{ name: '全部', selected: false }]
	secondArr.value = [{ name: '全部', selected: false }]
	thirdArr.value = [{ name: '全部', selected: false }]
	emotionTagsArr.value = [{ name: '全部', selected: false }]

	// 用于临时存储去重的分类值
	const genders = new Set()
	const sceneCategories = new Set()
	const recommendTagsList = new Set()
	const emotionTags = new Set()

	// 遍历数据提取唯一分类值
	data.forEach(item => {

		// 性别和年龄 - gender字段
		if (item.gender) {
			// 处理可能的多值情况，如"女、青年"
			const genderParts = item.gender.split('、')
			genderParts.forEach(part => genders.add(part))
		}

		// 场景分类 - sceneCategory字段
		if (item.sceneCategory) sceneCategories.add(item.sceneCategory)

		// 推荐标签 - recommendTags字段
		if (item.recommendTags) {
			// 处理可能的多值情况，先检查是否包含逗号，再检查是否包含顿号
			let tags = []
			if (typeof item.recommendTags === 'string') {
				if (item.recommendTags.includes(',')) {
					tags = item.recommendTags.split(',')
				} else if (item.recommendTags.includes('、')) {
					tags = item.recommendTags.split('、')
				} else {
					tags = [item.recommendTags]
				}
			} else if (Array.isArray(item.recommendTags)) {
				tags = item.recommendTags
			} else {
				tags = [item.recommendTags]
			}

			tags.forEach(tag => {
				if (tag && tag.trim() !== '') recommendTagsList.add(tag.trim())
			})
		}

		// 情感标签 - emotionTags字段
		if (item.emotionTags) {
			// 处理可能的多值情况，先检查是否包含逗号，再检查是否包含顿号
			let tags = []
			if (typeof item.emotionTags === 'string') {
				if (item.emotionTags.includes(',')) {
					tags = item.emotionTags.split(',')
				} else if (item.emotionTags.includes('、')) {
					tags = item.emotionTags.split('、')
				} else {
					tags = [item.emotionTags]
				}
			} else if (Array.isArray(item.emotionTags)) {
				tags = item.emotionTags
			} else {
				tags = [item.emotionTags]
			}

			tags.forEach(tag => {
				if (tag && tag.trim() !== '') emotionTags.add(tag.trim())
			})
		}
	})

	// 为性别筛选添加默认的"男"和"女"选项
	const defaultGenders = ['男', '女']
	defaultGenders.forEach(defaultGender => {
		if (!genders.has(defaultGender)) {
			genders.add(defaultGender)
		}
	})

	// 将去重后的分类值添加到相应数组
	genders.forEach(gender => {
		gendersArr.value.push({ name: gender, selected: false })
	})

	sceneCategories.forEach(category => {
		secondArr.value.push({ name: category, selected: false })
	})

	recommendTagsList.forEach(tag => {
		thirdArr.value.push({ name: tag, selected: false })
	})

	emotionTags.forEach(tag => {
		emotionTagsArr.value.push({ name: tag, selected: false })
	})

	// 处理第三层标签的去重和展示逻辑
	uniqueList.value = thirdArr.value.filter(
		(item, index) => thirdArr.value.findIndex(i => i.name === item.name) === index
	);
	thirdArrList.value = uniqueList.value

	// 保存初始分类数据
	initialCategories.genders.value = [...gendersArr.value]
	initialCategories.sceneCategories.value = [...secondArr.value]
	initialCategories.recommendTags.value = [...thirdArr.value]
	initialCategories.emotionTags.value = [...emotionTagsArr.value]

	// 初始化时调用一次更新函数来设置所有分类数据
	updateGenderOptions(data)
	updateSceneOptions(data)
}

// 按会员等级对音色进行排序的通用函数
const sortVoicesByMembership = (voices) => {
	// 复制数组以避免直接修改原数组
	const sortedVoices = [...voices];

	// 创建三个分组：SVIP、VIP和其他
	const svipVoices = sortedVoices.filter(item => item.membershipGrade === 'SVIP');
	const vipVoices = sortedVoices.filter(item => item.membershipGrade === 'VIP');
	const otherVoices = sortedVoices.filter(item => item.membershipGrade !== 'SVIP' && item.membershipGrade !== 'VIP');

	// 创建通用的recommendDegree比较函数
	const compareByRecommendDegree = (a, b) => {
		// 获取recommendDegree，没有则默认为999999（排在最后）
		const levelA = a.recommendDegree || 999999;
		const levelB = b.recommendDegree || 999999;
		// 升序排列（值小的排前面）
		return levelA - levelB;
	};

	// 在SVIP组内部，按照recommendDegree进行排序
	svipVoices.sort((a, b) => {
		return compareByRecommendDegree(a, b);
	});

	// 对VIP组内部进行排序
	vipVoices.sort((a, b) => {
		return compareByRecommendDegree(a, b);
	});

	// 对其他组内音色排序
	otherVoices.sort((a, b) => {
		// 首先按grade属性排序
		if (a.grade && b.grade && a.grade !== b.grade) {
			// 例如：让"精选"排在"甄选"前面
			if (a.grade === '精选' && b.grade === '甄选') return -1;
			if (a.grade === '甄选' && b.grade === '精选') return 1;
		}

		// 同一grade下按recommendDegree排序
		if (a.grade === b.grade) {
			return compareByRecommendDegree(a, b);
		}

		return 0; // 保持原有顺序
	});

	// 按SVIP > VIP > 其他的顺序合并数组
	return [...svipVoices, ...vipVoices, ...otherVoices];
}


const processedHTML = ref(null)
// getReplacedContent('editor', 'data-shuzi', '新文本');
function getReplacedContent(containerId, targetClass, newText) {
	// 获取原始容器元素并克隆副本‌:ml-citation{ref="1,6" data="citationList"}
	const originalDiv = document.getElementById(containerId);
	if (!originalDiv) return null;
	const clonedDiv = originalDiv.cloneNode(true);

	// 递归遍历克隆节点‌:ml-citation{ref="4,8" data="citationList"}
	const traverseAndReplace = (node) => {
		if (node.nodeType === Node.ELEMENT_NODE) {
			// 类名匹配检测‌:ml-citation{ref="1,3" data="citationList"}
			if (node.id == targetClass) {
				// if (node.classList?.contains(targetClass)) {
				node.textContent = newText; // 安全文本替换‌:ml-citation{ref="6,7" data="citationList"}
			}
			// 深度遍历子元素‌:ml-citation{ref="4,8" data="citationList"}
			Array.from(node.children).forEach(child => traverseAndReplace(child));
		}
	};

	traverseAndReplace(clonedDiv);
	return clonedDiv.innerHTML; // 返回处理后的副本内容‌:ml-citation{ref="6,8" data="citationList"}
}





// 数字符号
const figurePopover = ref(false)
const figureList = reactive([
	{ title: '数值', num: '四五四五四' },
	{ title: '数值', num: '四十五' },
])

const isNanFun = () => {



	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)


	if (!/^\d+$/.test(range.toString())) {
		ElMessage({
			message: '请选择纯数字文本',
			type: 'warning',
		})
		return
	} else {
		figureList[0].num = numberToPinyin(range.toString())
		figureList[1].num = numberToChinese(range.toString())
		figurePopover.value = true
	}
}
let popupAddClass=(type,className)=>{
	 if(type=='停顿'){
		return 'pause-popover'
	}else if(type=='读音替换'){
		return 'alias-popover'
	}else if(type=='数字符号'){
		return 'figure-popover'
	}else{
		if(className){
			return className
		}
	}
}
const clickFigure = (item) => {
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// 数字符号弹窗
	let id = nanoid(4)

	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span class='change'>" + item.num + "</span> <button style='font-size:14px; margin-left:5px;'>×</button>";
	tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid green";
	tipBox.style.backgroundColor = "#e0ffe0";
	tipBox.style.padding = "5px";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.fontSize = "14px";
	tipBox.style.display = "inline-block";
	tipBox.style.marginLeft = "5px";
	tipBox.style.cursor = "pointer";
	tipBox.style.pointerEvents = "auto";
	createUniqueArr.push(id)
	const span = document.createElement('span');
	span.className = id;
	span.id = id;
	range.surroundContents(span);
	// processedHTML.value = getReplacedContent('editor', 'data-shuzi', item.num);
	// console.log('processedHTML.value',processedHTML.value)
	var closeBtn = tipBox.querySelector("button");
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();

		// // 获取父节点及所有子节点‌:ml-citation{ref="8" data="citationList"}
		// const wrapper = document.createElement('span');
		// const parent = wrapperNode.parentNode;
		// const children = Array.from(wrapperNode.childNodes);
		//
		// // 在父节点中插入原始内容‌:ml-citation{ref="1,3" data="citationList"}
		// children.forEach(child => {
		//   parent.insertBefore(child, wrapperNode);
		// });
		//
		// // 移除包裹节点‌:ml-citation{ref="8" data="citationList"}
		// parent.removeChild(wrapperNode);


		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		// // 获取前一个元素节点‌:ml-citation{ref="1,8" data="citationList"}
		const prevElement = tipBox.previousSibling
		if (!prevElement) return;
		// // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		const fragment = document.createDocumentFragment();
		while (prevElement.firstChild) {
			fragment.appendChild(prevElement.firstChild);
		}
		// // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		prevElement.className = '';
		// // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		prevElement.parentNode.replaceChild(fragment, prevElement);
		// 、、、、、、、、、、、、、、、 清除添加的span标签但保留内容 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
		tipBox.parentNode.removeChild(span)
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	figurePopover.value = false
}

// 数字到中文字符映射
const numberMap = {
	'0': '零',
	'1': '一',
	'2': '二',
	'3': '三',
	'4': '四',
	'5': '五',
	'6': '六',
	'7': '七',
	'8': '八',
	'9': '九'
};
// 直接拼音转写
function numberToPinyin(num) {
	const numStr = String(num);
	let result = '';
	for (const char of numStr) {
		result += numberMap[char];
	}
	return result;
}
function numberToChinese(num) {
	const numberMap = {
		'0': '零',
		'1': '一',
		'2': '二',
		'3': '三',
		'4': '四',
		'5': '五',
		'6': '六',
		'7': '七',
		'8': '八',
		'9': '九'
	};

	const unitMap = {
		0: '',
		1: '十',
		2: '百',
		3: '千',
		4: '万',
		5: '十万',
		6: '百万',
		7: '千万',
		8: '亿'
	};

	let numStr = String(num);
	let result = '';
	let length = numStr.length;

	for (let i = 0; i < length; i++) {
		const currentDigit = numStr[i];
		const position = length - i - 1;

		if (currentDigit === '0') {
			// 当前位是零，且不在最高位
			if (i !== 0) {
				result += numberMap[currentDigit];
			}
			continue;
		}

		let part = '';

		part += numberMap[currentDigit];

		if (position > 0) {
			part += unitMap[position] || '';
		}

		result += part;
	}

	return result === '' ? '零' : result;
}









// 停顿弹窗
const stopPopover = ref(false)
const standstillList = reactive([
	'200', '400', '600', '800', '1000',
])
const clickStandstill = (item) => {
	// console.log(item)
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	var tipBox = document.createElement("div");
	tipBox.innerHTML = "<span>" + item + "m</span> <button data-type='number' style='font-size:10px;'>×</button>";
	// tipBox.setAttribute("data-type", "number");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.pointerEvents = "auto";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
	});
	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});
	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	console.log('stopPopover1');

	// 关闭弹窗
	stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}


// 弹窗位置
let topPopover = ref(0)
let leftPopover = ref(0)
let lettPopover_pause = ref(0)
const selection = window.getSelection();
const mouseup = (e) => {
	if (selection.rangeCount > 0) {
		// 动态改变多音字弹窗位置
		topPopover.value = e.pageY + 20


		// leftPopover.value = e.pageX-20
		// if(progress_barNum.value==3){
		//   leftPopover.value = e.pageX-300
		// }else{
		leftPopover.value = e.pageX - 40
		lettPopover_pause.value = e.pageX - 14
		// }

		console.log('99999999999', leftPopover)
		// range.surroundContents(span);
		// document.getElementById('editor').appendChild(span)
		// range.insertNode(span); // 将带有样式的span重新插入到文档中。
		// // 重新设置选择范围以包含整个span。
		// range.setStartAfter(span); // 移动到span之后。
		// range.setEndAfter(span); // 同样移动到span之后。
		// selection.removeAllRanges(); // 清除之前的选中范围。
		// selection.addRange(range); // 设置新的选中范围。
	}
}

// 移除可编辑div下span中存在的文字并把文字移到文本节点中
// function clearHighlights() {
//   document.querySelectorAll('#editor span').forEach(span => {
//     span.outerHTML = span.innerHTML;
//   });
// }

let selectionRange; // 存储选区范围以便恢复
const handleClick = (event) => {
	show_suspended_toolbar.value=true
	const selection = window.getSelection();
	selectionRange = selection.getRangeAt(0).cloneRange(); // 保存当前选区范围
	// 你可以在这里添加其他逻辑，例如阻止默认行为等。
	event.preventDefault();
}
// 恢复选取
const restoreSelection = () => {
	if (selectionRange) {
		const selection = window.getSelection();
		selection.removeAllRanges(); // 清除现有选区
		selection.addRange(selectionRange); // 恢复选区范围
	}
}


























// 事件代理模式（父容器监听）
const handleContainerClick = (event) => {
	// stopPopover.value = false
	// console.log('8888888888888',event)
	// console.log('oooooo',event.target.closest('[data-id]'))
	const target = event.target.closest('[data-id]');
	// console.log('454545',target)
	// is_show_volume.value = false
	try {
		if (target) {

		} else {
			console.log('stopPopover2');
			// 关闭停顿弹窗
			stopPopover.value = false
			showPopover.value = false
			figurePopover.value = false
			aliasPopover.value = false
		}
		// if(!target){
		//   if(target.dataset.id!=='parent-3'){
		//     stopPopover.value = false
		//   //   var div = document.querySelector('div[contenteditable="true"]');
		//   //   // console.log('77',div)
		//   //   if (div) { // 检查div是否存在
		//   //     var children = div.children;
		//   //     // console.log('ppp',children)
		//   //     for (var i = 0; i < children.length; i++) {
		//   //       children[i].classList.remove('tts-tag');
		//   //     }
		//   //   }
		//   }
		// }else{
		//   stopPopover.value = true
		// }

		// showPopover.value = false

	} catch (e) {
		// console.log('ppp',e)
		// var div = document.querySelector('div[contenteditable="true"]');
		// // console.log('77',div)
		// if (div) { // 检查div是否存在
		//   var children = div.children;
		//   // console.log('ppp',children)
		//   for (var i = 0; i < children.length; i++) {
		//     children[i].classList.remove('tts-tag');
		//   }
		//
		// }
		// showPopover.value = false
	}
};



// 点击顶部下载按钮
const clickDownlaodButton = (e, index) => {
	if (index == 0 && audioUrl.value == '') {
		ElMessage({
			message: '暂无音频文件',
			type: 'warning',
		})
		return
	}
	if (index == 1 && captions_url.value == '') {
		ElMessage({
			message: '暂无字幕文件',
			type: 'warning',
		})
		return
	}
	const link = document.createElement('a');
	if (index == 0) {
		link.href = audioUrl.value; // 文件 URL
		link.download = 'document.mp3'; // 默认文件名
	} if (index == 1) {
		link.href = captions_url.value; // 文件 URL
		link.download = 'document.txt'; // 默认文件名
	}
	link.style.display = 'none';
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}





// 点击音乐面板中的喇叭按钮
const is_show_volume = ref(false)
const progress_barNum4_slideValue = ref(60)
const click_volume = () => {
	is_show_volume.value = !is_show_volume.value
}
// 点击关闭音乐面板中的叉号按钮
const close_music_div = () => {
	is_show_volume.value = false
	useAIDubbing.bgmusic_url = ''
	useAIDubbing.bgmusicObj = {}
	useCommerDubbing.bgmusic_url = ''
	useCommerDubbing.bgmusicObj = {}

}







// 返回的音频文件
const audioUrl = ref('')
// 返回的字幕文件
const captions_url = ref('')


// 合成语音
// 加载动画
const loading = ref(false)
const progress_barNum0_right_button = ref(false)
const trial_listening = ref(false)
// 合成样音时保存选中的音色列表
const selected_timbre_arr = ref([])
const selected_timbre_filter_arr = ref([])
// const htmlContent = '<div id="container"><p>Hello, world!</p><p>Another paragraph.</p></div>';
const progress1_loading = ref(false)
let synthetic_button_type = ref()
let textInfo = ''
let handle_selectedTextList = []
// bool == true 合成样音调另一个接口  param 据此判断是否是快速试听
const syntheticAudioButton = async (type, bool, param, index) => {
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
	audioRef.value.src = ''
	audioRef.value.pause()
	isPauseTtsAudio.value = false
	audioRef.value.src = ''
	audioRef.value.pause()
	progress_barNum_0_audioUrl.value=''
	// 埋点
	commercialDubbingAnalytics.trackSampleAudio(SoundItemId.value);
	if (Array.isArray(AudioPlayerRef.value)) {
		AudioPlayerRef.value.forEach(item => {
			item.handleCloseMusic()
		})
	} else if (AudioPlayerRef.value) {
		AudioPlayerRef.value.handleCloseMusic()
	}



	if (!localStorage.getItem('user')) {
		proxy.$modal.open()
		return
	}
	if (sample_Sound_List.value.length) {
		sample_Sound_List.value.map(item => {
			item.isPauseTtsAudio = true
		})
	}
	if (sampleValue.value == false && progress_barNum.value == 1) {
		useCommerDubbing.select_timbre_itemIndex = index
	}




	// 只要点击合成和试听按钮就全部停掉音色列表中播放
	stop_timbre_play()
	if (type == 1) {
		//   合成音频单人 1  true  2
		if (bool && param == 2) {
			// progress_barNum0_right_button.value = true
			loading.value = true
		} else {
			// loading.value = true
			progress_barNum0_right_button.value = true
		}
	} else {
		// 快速试听 2  false  1
		trial_listening.value = true
	}
	if (param == 3) {
		progress_barNum4_Loading.value = true
	}
	if (param == 5) {
		SoundItem.value=selected_timbre_arr.value[index]
		console.log(selected_timbre_arr.value,index);

		if(selected_timbre_arr.value[index].saveId){
			saveId.value=selected_timbre_arr.value[index].saveId
		}
		// progress1_loading.value = true
		if (index || index == 0) {
			sample_Sound_List.value[index].loading = true
		}
	}

	//只有多人出样的时候才需要过滤或者不是我的空间跳转
	if (!sampleValue.value&&!route.query.batchId) {
		selected_timbre_arr.value = soundList.value.filter(item => item.isSelected)
		console.log('78888888', selected_timbre_arr.value)
		selected_timbre_filter_arr.value = []
		selected_timbre_filter_arr.value.length = 0
		console.log('selected_timbre_arr', selected_timbre_arr)
		selected_timbre_arr.value.map(item => {
			selected_timbre_filter_arr.value.push(item.voiceName)
		})
	}


	// // 通过CSS属性选择器定位目标元素‌:ml-citation{ref="3,4" data="citationList"}
	const container = document.querySelector('#editor');  // 目标父级div
	// // 提取ID名称集合
	//   const elements_data_IdList = [];
	//   elements_data_Id.forEach(element => {
	//     console.log(element.dataset)
	//     // if (element['dataset']) elements_data_IdList.push(element['dataset'].name);  //
	//   });
	// 获取音频链接
	// const audio_list = []
	// const create_background_divs = editorRef.value.getElementsByClassName('create_background');
	// create_background_divs.forEach(element => {
	//   console.log(element)
	//   // if (element['dataset']) audio_list.push(element['dataset'].id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	// });
	const elementsWithId = container.querySelectorAll('[id]');  // 获取所有带id属性的子元素‌:ml-citation{ref="3,4" data="citationList"}
	// // 提取ID名称集合
	const idList = [];
	elementsWithId.forEach(element => {
		if (element.id) idList.push(element.id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	});
	// const targets = editorRef.value.getElementsByClassName('data-shuzi');
	const changContents = editorRef.value?.getElementsByClassName('change');
	// const nanoidArray = []
	// for(let i=0;i<idList.length;i++){
	//   nanoidArray.push(nanoid(4))
	// }
	let originalArr = []
	Array.from(elementsWithId).forEach((el, idx) => {
		originalArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	let alternativeArr = []
	if (changContents) {
		Array.from(changContents).forEach((el, idx) => {
			alternativeArr.push({
				id: idList[idx],
				text: el.innerText,
			})
		});
	}
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	if (alternativeArr.length > 0) {
		for (let j = 0; j < originalArr.length; j++) {
			processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
			if (parentDiv) {
				const elements = parentDiv.querySelectorAll('*');
				elements.forEach(element => {
					if (element.id === alternativeArr[j].id) {
						element.textContent = alternativeArr[j].text; // 替换文本
					}
				});
				// processedHTML.value = elements
				// const targetDivs = parentDiv.querySelectorAll("div[data-type]");
				// targetDivs.forEach(div => div.remove());
			}
		}
	}
	// for(let j=0;j<originalArr.length;j++){
	//   processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
	//   if (parentDiv) {
	//     const elements = parentDiv.querySelectorAll('*');
	//     elements.forEach(element => {
	//         if (element.id === alternativeArr[j].id) {
	//           element.textContent = alternativeArr[j].text; // 替换文本
	//         }
	//     });
	//     // processedHTML.value = elements
	//     // const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	//     // targetDivs.forEach(div => div.remove());
	//   }
	// }
	const elements = parentDiv.querySelectorAll("[data-type='number11']");
	console.log('elements', elements)
	const parentElement = document.getElementsByClassName('parent')
	const dataIdValues = [];
	Array.from(parentElement).forEach(parent => {
		const spans = parent.getElementsByTagName('span'); // 获取当前 parent 下的所有 span 元素
		Array.from(spans).forEach(span => {
			const dataId = span.getAttribute('data-id');
			// 如果 data-id 存在，则添加到数组中
			if (dataId) {
				dataIdValues.push(dataId);
			}
		});
		// const spans = parent.querySelectorAll('span');
		// // 遍历每个 span 元素
		// spans.forEach(span => {
		//   // 获取 span 元素的 data-id 属性值
		//   const dataId = span.getAttribute('data-id');
		//   // 如果 data-id 存在，则添加到数组中
		//   if (dataId) {
		//     dataIdValues.push(dataId);
		//   }
		// });
	});
	// 获取到的音效链接
	Array.from(elements).map((element, index) => {
		var linkElement = document.createElement('a');
		// linkElement.href = dataIdValues[index]; // 设置链接地址
		linkElement.textContent = '[sound:' + dataIdValues[index] + ']'; // 复制 div 的内容到链接
		// linkElement.style.backgroundColor = 'lightgreen'; // 设置链接背景颜色
		// element.textContent = dataIdValues[index]; // 替换内容
		// 用链接替换 div
		element.replaceWith(linkElement);
	})
	// const targetDivs = parentDiv.querySelectorAll('div:not([data-type="number11"])');
	const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	targetDivs.forEach(div => div.remove());
	const targetDivs1 = parentDiv.querySelectorAll("button[data-type]");
	targetDivs1.forEach(div => div.remove());
	// 需要传的字符串信息   注意  其中有数字就会转  需要处理
	textInfo = ''
	if (progress_barNum.value == 0) {
		textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
			// console.log(parseInt(val))
			return `<#${parseInt(val) / 1000}#>`
		})
		// 把换行替换成字符串
		textInfo = textInfo.replace(/\n/g, '');
		// 保存未切割的字符串
		useAIDubbing.textInfo = textInfo.replace(/\n/g, '');
	}
	// 步骤er中合成按钮
	if (progress_barNum.value == 1) {
		textInfo = useAIDubbing.textInfo
	}



	// sampleValue.value==false
	if (type == 2 ||type == 3 || param == 2) {
		let parts = textInfo.split(/(\[sound:.*?\])+/);
		let finalText = parts.filter(part => part.trim() !== '')
		console.log('finalText', finalText)
		let length = 0
		for (let i = 0; i < finalText.length; i++) {
			// console.log(finalText[i])
			if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
				if (finalText[i].length <= 200) {
					length += finalText[i].length
				} else {
					finalText[i] = finalText[i].substring(0, 200 - length);
				}
			}
		}
		textInfo = finalText.join('')
		if(type==3){
			textInfo=textAfterCaret.value
		}
	}
	console.log('textInfo', textInfo)


	if (!textInfo) {
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		loading.value = false
		progress_barNum0_right_button.value = false
		trial_listening.value = false
		return
	}
	// 判断是多人出样还是单人出样
	if (sampleValue.value) {
		if (!SoundItemId.value) {
			ElMessage({
				message: '请先选择声音',
				type: 'warning',
			})
			loading.value = false
			progress_barNum0_right_button.value = false
			trial_listening.value = false
			return
		}
	} else {
		if (selected_timbre_filter_arr.value.length == 0) {
			ElMessage({
				message: '请至少选择一个声音',
				type: 'warning',
			})
			loading.value = false
			progress_barNum0_right_button.value = false
			trial_listening.value = false
			return
		}
	}

	handle_selectedTextList = []
	selectedTextList.map(item => {
		handle_selectedTextList.push(item.name)
	})
	synthetic_button_type.value = type
	full_loading.value=true
	//检测全部字符串中字符 ， 看是否有敏感词，只检测第一步中的所有字符
	if (progress_barNum.value == 0) {
		checkSensitiveWords(type, bool, param, index)
	} else {
		popUp_query_button(type, bool, param, index)
	}
	// type,bool,param,index
	// let obj = {
	//   user_id: JSON.parse(localStorage.getItem('user'))?.userId || '',
	//   text: textInfo,
	//   audio_format:"mp3",
	//   chunk_size:2048,
	//   speed:progress_barNum.value==3 ? progress_barNum3_speechValue.value : speechValue.value, //语速
	//   vol:slideValue.value/10, //音量
	//   pitch:progress_barNum.value==3 ? progress_barNum3_intonationValue.value : intonationValue.value,  //语调
	//   pronunciation:handle_selectedTextList, //多音字列表
	//   model:'speech_SFT_01',
	//   // bgm_url:useAIDubbing.bgmusic_url
	// }
	// if(sampleValue.value){
	//   // 单人合成配音
	//   if(param==2){
	//     obj.voice_ids = [SoundItemId.value]
	//   }else{
	//     obj.voice_id = SoundItemId.value
	//   }
	// }else{
	//   if(param==5){
	//     obj.voice_id = selected_timbre_arr.value[index].voiceName
	//     // debugger
	//   }else if(param==3){
	//     obj.voice_id = selected_timbre_arr.value[useCommerDubbing.select_timbre_itemIndex].voiceName
	//   }else{
	//     // 多人
	//     obj.voice_ids = selected_timbre_filter_arr.value
	//   }
	// }
	// if(bool){
	//   Multiple_speech(obj,param,index)
	// }else{
	//   single_speech(obj,type,bool,param)
	// }
}

// 点击检测弹窗中的确定按钮
const popUp_query_button = (type, bool, param, index) => {
	console.log(type, bool, param, index,'popUp_query_button');

	let obj = {
		user_id: JSON.parse(localStorage.getItem('user'))?.userId || '',
		text: textInfo,
		audio_format: "mp3",
		chunk_size: 2048,
		speed: progress_barNum.value == 3 ? progress_barNum3_speechValue.value : speechValue.value, //语速
		vol: slideValue.value / 10, //音量
		pitch: progress_barNum.value == 3 ? progress_barNum3_intonationValue.value : intonationValue.value,  //语调
		pronunciation: handle_selectedTextList, //多音字列表
		model: 'speech_SFT_01',
		vol_main:volumeValue.value/100, //背景音量
		// bgm_url:useAIDubbing.bgmusic_url
	}
	if (sampleValue.value) {
		// 单人合成配音
		if (param == 2) {
			obj.voice_ids = [SoundItemId.value]
		} else {
			obj.voice_id = SoundItemId.value
		}
	} else {
		if (param == 5) {
			obj.voice_id = selected_timbre_arr.value[index].voiceName

			// debugger
		} else if (param == 3) {
			obj.voice_id = selected_timbre_arr.value[useCommerDubbing.select_timbre_itemIndex].voiceName
		} else {
			// 多人
			obj.voice_ids = selected_timbre_filter_arr.value
		}
	}
	if (synthetic_button_type.value == 2) {
		obj.isTrialListen = true
	}
	console.log(77,param);

	if (bool) {
		Multiple_speech(obj, param, index)

	} else {
		single_speech(obj, type, bool, param)
	}
}
//音色包合成校验
let packCheck = (voice_ids) => {
	// console.log(soundStore.packChooseData,voice_ids,'packCheck');

	if (route.query.package && soundStore.packChooseData.length > 0) {
		let result = soundStore.packChooseData.findIndex(item => item.voiceName == voice_ids)
		return result >= 0 ? true : false
	}
}




const Multiple_speech = (obj, index) => {
	console.log(index,'999');

	// handleBatchCreateAPI()
	generateAudiosApi(obj).then(async(res) => {
		console.log('77', res)
		full_loading.value=false
		let { data, code } = res
		// console.log('78979846',data,code)
		if (code == 0) {
			let { status_code, content } = data || {}
			// console.log('llll',status_code, content,content.length)
			if (status_code == 200) {
				let { result } = content
				// console.log('oooo787898798',result)
				if (result) {

					// console.log('result',result)
					// if(sampleValue.value){
					//   audioUrl.value = result.audio_file
					//   captions_url.value = result.subtitle_file
					// }
					if (result.length > 0) {
						await fetchUserBenefits()

						result.map(item => {
							item.isPauseTtsAudio = false
						})
						sample_Sound_List.value = result

						sample_Sound_List.value.map(item => {
							item.loading = false
						})
						// 关闭第一个步骤中的底部的试听音频
						isPauseTtsAudio.value = !isPauseTtsAudio.value
						progress_barNum.value = 1
						progress_bar_arr.value[progress_barNum.value - 1].isSelected = true
						let success_msg=''
						console.log(progress_barNum_3_result_subtitle_json.value,progress_barNum3_activeIndex.value,'设置');


						if(index==3){
							success_msg='重新合成成功'
						}
						if(index==2||index==4||index==5){
							success_msg='合成成功'
						}
						if(synthetic_button_type.value==2){
							success_msg='操作成功'
						}
						ElMessage({
							message: success_msg,
							type: 'success',
						})
						//如果是第二部合成成品需要走保存
						if(index==5){
							batch_save_progress(3)
						}
					} else {
						ElMessage({
							message: '音频转义失败，请重新上传',
							type: 'warning',
						})
					}
					progress1_loading.value = false
					console.log(sample_Sound_List.value,index);

					// sample_Sound_List.value[index].loading = false
					// console.log('audioUrl.value',audioUrl.value)
				} else {
					audioUrl.value = ''
					captions_url.value = ''
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
				loading.value = false
				trial_listening.value = false
			} else {

				audioUrl.value = ''
				captions_url.value = ''
				loading.value = false
				trial_listening.value = false
				full_loading.value=false
				if (alert_dialog_code_arr.value.includes(status_code)) {

					public_open_alert_dialog(status_code,obj)
					return
				} else {
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
			}
		} else {
			audioUrl.value = ''
			captions_url.value = ''
			loading.value = false
			trial_listening.value = false
			ElMessage({
				message: '音频转义失败，请重新上传',
				type: 'warning',
			})
		}
	}).catch((err) => {
		console.log(err,'err');
		full_loading.value=false
		if (alert_dialog_code_arr.value.includes(err.response.status_code)) {
			public_open_alert_dialog(err.response.status_code,obj)
		}
		console.log('11', err)
	})
}

// 检查是否包含敏感词
const checkSensitiveWords = async (type, bool, param, index) => {
	let txt=type==3?textAfterCaret.value:useAIDubbing.textInfo
	// 检查输入的文本是否包含敏感词
	const res = await chekSensitive_Api({
		txt: txt.replace(/\[sound:[^\]]*\]|<#.*?#>/g, '').trim()
	})
	const {
		code,
		data // 不直接解构，先检查 data
	} = res || {}; // 如果 res 为 null，则使用空对象

	const result = data?.content?.result ?? null; // 使用可选链和空值合并运算符
	const status_code = data?.status_code;
	// console.log('result',res)
	if (result && result.length > 0) {
		ElMessageBox.confirm(
			`文本中包含${result.join('、')}敏感词，是否继续生成音频？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				// type: 'warning',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				showClose: false,
				beforeClose: (action, instance, done) => {
					if (action === "confirm") {
						instance.confirmButtonLoading = true;
						instance.confirmButtonLoading = false
						done()
						popUp_query_button(type, bool, param, index)
					} else {
						cancel_check()
						done()

					}
				}

			}
		).then(res => {
			// console.log('77777777777')
		}).catch(err => {
			loading.value = false
			trial_listening.value = false
			progress_barNum0_right_button.value = false
		})
	} else {
		//直接合成音频
		popUp_query_button(type, bool, param, index)
	}
}
//map转wav音频文件
let map_to_wav=(url)=>{
	return new Promise(async(resolve, reject) => {
		let data=await extractWavByMp3({
			userId: JSON.parse(localStorage.getItem('user'))?.userId || '', //用户id
			audioUrl:url
		})
		console.log(data,'data');

		if (data.code === 0) {
			resolve(data.data.content.result || '')
		} else {
			ElMessage({ message: data.msg || '处理失败', type: 'warning' });
			resolve('')
		}
	})

}
// 点击步骤三中的下载
const download_mp3 = async(e) => {
	synthetic_button_type.value = ''
	// handleBatchCreateAPI()
	let audio_url=progress_barNum_2_result.value.audio_file
	let suffix='mp3'
	if(e.index==1){
		audio_url=await map_to_wav(audio_url)
		suffix='wav'
	}
	try {
    const response = await fetch(audio_url);
    if (!response.ok) throw new Error('网络请求失败');

    const blob = await response.blob();

    const url = URL.createObjectURL(blob);

    const filename = `${progress_barNum_2_result.value.subtitle_json[0].text.slice(0,5)}—${SoundItem.value.platformNickname}.${suffix}`
      .replace(/[\/\\:*?"<>|]/g, '_'); // 过滤非法字符

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);

  } catch (error) {
    console.error('下载失败:', error);
  }
}

const download_mp4 = async(e) => {
	let txt=""
	let audio_url=progress_barNum_4audioUrl.value
	let suffix='mp3'
	if (audio_url == '') {
		ElMessage({
			message: '请先合成成品，暂无音频文件',
			type: 'warning',
		})
		return
	}
	if(e.index==1){
		audio_url=await map_to_wav(audio_url)
		suffix='wav'
	}
	progress_barNum_3_result_subtitle_json.value.map((item,index)=>{
		txt=txt+item.text
	})
	synthetic_button_type.value = ''
	// handleBatchCreateAPI()
	try {
		console.log(audio_url,'progress_barNum_4audioUrl');

	const response = await fetch(audio_url);
    if (!response.ok) throw new Error('网络请求失败');

    const blob = await response.blob();

    const url = URL.createObjectURL(blob);

    const filename = `${txt.slice(0,5)}—${SoundItem.value.platformNickname}.${suffix}`
      .replace(/[\/\\:*?"<>|]/g, '_'); // 过滤非法字符

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);

  } catch (error) {
    console.error('下载失败:', error);
  }
}



// 点击步骤三中的编辑修改
// 步骤四中的变量
const progress_barNum_3_result = ref({})
const progress_barNum_3_result_oss_paths = ref([])
const progress_barNum_3_result_subtitle_json = ref([])
const progress_barNum2_loading = ref(false)
const edit_timbre_progress_barNum2 = () => {
	full_loading.value=true
	progress_barNum2_loading.value=true
	clip_audio_file_Api({
		miniData: progress_barNum_2_result.value
	}).then(async(res) => {
		const {
			code,
			data: {
				content: { result = null } = {},
				status_code
			} = {}
		} = res || {};
		progress_barNum_3_result.value = result
		// audio_file
		progress_barNum_3_result_oss_paths.value = result?.oss_paths || []
		progress_barNum_3_result_subtitle_json.value = result?.subtitle_json || []

		progress_barNum_3_result_subtitle_json.value.map(item => {
			item.isPlay = false
			item.speed=speechValue.value
			item.pitch=intonationValue.value
		})
		// editorRef.value.innerHTML = progress_barNum_3_result_subtitle_json.value[0].text
		document.getElementById('editor11').innerHTML = progress_barNum_3_result_subtitle_json.value[0].text
		if (code == 0 && status_code == 200) {
			await nextTick()
			showAudio.value = false
			setTimeout(() => {
				showAudio.value = true
			}, 500)  // 先移除再重新渲染
			isPauseTtsAudio.value=false
			progress_barNum.value = 3
			progress_bar_arr.value[progress_barNum.value - 1].isSelected = true
			isPauseTtsAudio.value = true

			console.log('关闭')

		}
		progress_barNum3_speechValue.value=progress_barNum_3_result_subtitle_json.value[0].speed
		progress_barNum3_intonationValue.value=progress_barNum_3_result_subtitle_json.value[0].pitch
		full_loading.value=false
		progress_barNum2_loading.value = false
		console.log(res)
	}).catch(err => {
		full_loading.value=false
		progress_barNum2_loading.value = false
		console.log(err)
	})
}


// 点击步骤四中列表中的播放按钮
const progress_barNum3_activeIndex = ref(0)
// 点击切换item
const change_progress_barNum3_item = (index) => {
	progress_barNum3_activeIndex.value = index
	document.getElementById('editor11').innerHTML = progress_barNum_3_result_subtitle_json.value[index].text
	progress_barNum3_speechValue.value=progress_barNum_3_result_subtitle_json.value[index].speed
	progress_barNum3_intonationValue.value=progress_barNum_3_result_subtitle_json.value[index].pitch
	// editorRef.value.innerHTML = progress_barNum_3_result_subtitle_json.value[index].text
}
let current_index=ref(0)
const play_audio_progress_barNum3 = (index) => {
	AudioPlayerRef.value.handleCloseMusic()
	current_index.value = index
	progress_barNum_3_result_subtitle_json.value.map((child, idx) => {
		if (index == idx) {
			if (progress_barNum_3_result_subtitle_json.value[idx].isPlay) {
				progress_barNum_3_result_subtitle_json.value[idx].isPlay = false
				audioRef.value.pause()
			} else {
				progress_barNum_3_result_subtitle_json.value[idx].isPlay = true
				audioRef.value.src = progress_barNum_3_result_oss_paths.value[index]
				nextTick(() => {
					audioRef.value.play()
				})
			}
		} else {
			progress_barNum_3_result_subtitle_json.value[idx].isPlay = false
		}
	})
}

// 下载单个音频
const progress_barNum3_download_text = async(index) => {
	synthetic_button_type.value = ''
	// handleBatchCreateAPI()
	try {
    const response = await fetch(progress_barNum_3_result_oss_paths.value[index]);
    if (!response.ok) throw new Error('网络请求失败');

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);

    const filename = `${progress_barNum_3_result_subtitle_json.value[index].text.slice(0, 5)}—${SoundItem.value.platformNickname}.mp3`
      .replace(/[\/\\:*?"<>|]/g, '_'); // 过滤非法字符

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);

  } catch (error) {
    console.error('下载失败:', error);
  }
}




// 生成单人声音
const progress_barNum_0_audioUrl = ref('')
const progress_barNum_2_audioUrl = ref('')
const progress_barNum_2_captions_url = ref('')
const progress_barNum_2_result = ref({})

const progress_barNum_4audioUrl = ref('')
const progress_barNum_4result = ref({})
const progress_barNum4_Loading = ref(false)
const single_speech = (obj, type, bool, param) => {
console.log('single_speech',obj, type, bool, param);


	// handleBatchCreateAPI()
	synthesized_speechApi(obj).then(async(res) => {
		full_loading.value=false
		// console.log('77',res)
		let { data, code } = res
		// console.log('78979846',data,code)
		if (!data) {
			progress_barNum0_right_button.value = false
			ElMessage({
				message: '请重试',
				type: 'warning',
			})
			progress_barNum4_Loading.value = false
			return
		}
		if (code == 0) {
			let { status_code, content } = data
			// console.log('llll',status_code, content,content.length)
			if (status_code == 200) {
				let { result } = content
				// console.log('oooo787898798',result)
				if (result) {
					await fetchUserBenefits()
					if (sampleValue.value && bool) {
						audioUrl.value = result.audio_file
						captions_url.value = result.subtitle_file
					}
					// 1是快速试听 2是单人合成样音  3是重新合成
					if (param == 1) {
						trial_listening.value=false
						progress_barNum_0_audioUrl.value = result.audio_file
						captions_url.value = result.subtitle_file
						ElMessage({
							message: '操作成功',
							type: 'success',
						})
					}
					if (bool == false && param !== 1) {

						if (param == 2) {
							progress_barNum.value = 1
							progress_bar_arr.value[progress_barNum.value - 1].isSelected = true
							// 关闭第一个步骤中的底部的试听音频
							isPauseTtsAudio.value = !isPauseTtsAudio.value
						}

						// else{
						//   progress_barNum.value = 2
						// }
						progress_barNum_2_result.value = result
						if (param != 3) {
							progress_barNum_2_audioUrl.value = result.audio_file
							progress_barNum_2_captions_url.value = result.subtitle_file
						}
						if (param == 5) {
							progress_barNum.value = 2
							progress_bar_arr.value[progress_barNum.value - 2].isSelected = true
							progress_bar_arr.value[progress_barNum.value - 1].isSelected = true
							progress_barNum_2_audioUrl.value = result.audio_file
							progress_barNum_2_captions_url.value = result.subtitle_file
						}
						if(param!=3){
							ElMessage({
								message: '合成成功',
								type: 'success',
							})
						}

					}
					// debugger
					// 第一步合成音频
					if (param == 4) {
						progress_barNum.value = 2
						progress_bar_arr.value[progress_barNum.value - 2].isSelected = true
						progress_bar_arr.value[progress_barNum.value - 1].isSelected = true
						if(bool){
							ElMessage({
								message: '合成成功',
								type: 'success',
							})
						}


					}
					if (param == 3) {
						// subtitle_json[0].text
						// progress_barNum_4audioUrl.value = result.audio_file
						// progress_barNum_4result.value = result
						// 把重新生成的音频文件以及文字覆盖掉原来的,加了停顿之后会显示多条记录，需要循环显示出来
						let textString = ''
						if (result.subtitle_json.length > 1) {
							result.subtitle_json.map(item => {
								textString += item.text
							})
						} else {
							textString = result.subtitle_json[0].text
						}
						progress_barNum_3_result_subtitle_json.value[progress_barNum3_activeIndex.value].text = textString.replace(/<#\d+\.?\d*#>/g, '')
						progress_barNum_3_result_oss_paths.value[progress_barNum3_activeIndex.value] = result.audio_file
						progress_barNum_3_result_subtitle_json.value[progress_barNum3_activeIndex.value].speed=obj.speed
						progress_barNum_3_result_subtitle_json.value[progress_barNum3_activeIndex.value].pitch=obj.pitch
						ElMessage({
							message: '重新合成成功',
							type: 'success',
						})
					}
					//如果是第二部合成成品需要走保存
					if(param==5){
						batch_save_progress(3)
					}
				} else {
					audioUrl.value = ''
					captions_url.value = ''
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
				loading.value = false
				progress_barNum0_right_button.value = false
				trial_listening.value = false
				progress_barNum4_Loading.value = false

			} else {

				audioUrl.value = ''
				captions_url.value = ''
				loading.value = false
				progress_barNum0_right_button.value = false
				trial_listening.value = false
				progress_barNum4_Loading.value = false
				full_loading.value=false
				if (alert_dialog_code_arr.value.includes(status_code)) {
					public_open_alert_dialog(status_code,obj)
					return
				} else {
					ElMessage({
						message: '音频转义失败，请重新上传',
						type: 'warning',
					})
				}
			}
		} else {
			audioUrl.value = ''
			captions_url.value = ''
			loading.value = false
			progress_barNum0_right_button.value = false
			trial_listening.value = false
			ElMessage({
				message: '音频转义失败，请重新上传',
				type: 'warning',
			})
		}
	}).catch((err) => {
		full_loading.value = false
		console.log(err,'err');
		if (alert_dialog_code_arr.value.includes(err.response.status_code)) {
			public_open_alert_dialog(err.response.status_code,obj)
		}
		console.log('11', err)
	})
}



// 当点击合成音频底部播放按钮时，暂停所有播放音色播放
const stop_timbre_play = () => {
	soundList.value.map((child, idx) => {
		soundList.value[idx].isPlay = false
	})
	console.log(audioRef.value,'audioRef.value');
	let audioRefArr=[]
	audioRefArr.push(audioRef.value)
	audioRef.value.src = ''
	audioRef.value.pause()
	onAudioEnded()
}

// 点击播放音色列表中某一项，播放
const AudioPlayerRef = ref([])
const audioRef = ref(null);
const playAudio = (item, index) => {
	audioRef.value.src = ''
	audioRef.value.pause()

	// 关闭页面底部合成音频播放
	isPauseTtsAudio.value = false


	if (Array.isArray(AudioPlayerRef.value)) {
		AudioPlayerRef.value.forEach(item => {
			item.handleCloseMusic()
		})
	} else if (AudioPlayerRef.value) {
		AudioPlayerRef.value.handleCloseMusic()
	}
	// isPauseTtsAudio.value = true
	// console.log('audioRef',audioRef.value)
	soundList.value.map((child, idx) => {
		if (item.id == child.id) {
			if (soundList.value[idx].isPlay) {
				soundList.value[idx].isPlay = false
				audioRef.value.pause()
			} else {
				soundList.value[idx].isPlay = true
				audioRef.value.src = item.audioUrl
				nextTick(() => {
					audioRef.value.play()
				})
			}
		} else {
			soundList.value[idx].isPlay = false
		}
	})
}
// 选择音效时监听接收的数据
// eventBus.on('update-data', (data) => {
//   console.log('接收数据4545:', data); // 输出：1
//   //插入音效到页面指定位置
//   click_effects_item(data)
//
// });
// onUnmounted(() => { // 清理监听
//   eventBus.off('update-data');
// });
// watch(()=>useAIDubbing.sound_effects_url_obj,(newVal)=>{
//   console.log('787878',newVal)
// })
// 导入文案监听
const handleCallLetter = (data) => {
	// console.log('8643541635456',data)
	editorRef.value.innerHTML = data;
	editorRef.value.dispatchEvent(new InputEvent('input'));
}


const Letter = ref(null)
const effects = ref(null)
const handleCallParent = (data) => {
	// console.log('收到子组件事件:', data);
	click_effects_item(data)

};

const click_effects_item = (item) => {
	// console.log(item)
	let value_url = item.storagePath || item.ossPath
	restoreSelection()
	const selection = window.getSelection();
	const range = selection.getRangeAt(0)
	// console.log('llllllll888',range.toString())
	// var selectedText = range.toString();
	// console.log('lll777',/^\\d+$/.test(selectedText))
	// if (/^\\d+$/.test(selectedText)) {
	// var richTextDiv = document.getElementById("rich-text");
	var richTextDiv = editorRef.value;
	if (!richTextDiv) {
		ElMessage({
			message: '无法找到富文本编辑区',
			type: 'warning',
		}); return "";
	}
	// const div = document.createElement('div');
	// div.style.width = '60px';
	// div.style.height = '30px';
	// div.style.padding = "0 5px";
	// div.style.marginLeft = "5px";
	// div.style.backgroundImage = 'url(http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D)';
	// div.style.backgroundSize = '20px 20px';
	// div.style.backgroundRepeat = "no-repeat";
	// // div.style.backgroundColor = "#eff8f2";
	// div.style.border = "1px solid #ddd";
	// div.style.backgroundColor = "#ddd";
	// // div.style.backgroundPosition = "center left";
	// div.style.backgroundPosition = "5px center";
	// div.style.borderRadius = "6px";
	// div.setAttribute("data-type", "number");
	// div.setAttribute("contentEditable", "false");
	// div.style.display = "inline-block";
	// div.style.position = "relative";
	// div.style.pointerEvents = "auto";
	// // div.innerHTML = "<button data-type='effect' style='font-size:10px;'>×</button>";
	// // var closeBtn = tipBox.querySelector("button");
	// // closeBtn.style.border = "none";
	// // closeBtn.style.padding = "4px";
	// // closeBtn.style.fontSize = "14px";
	// // closeBtn.style.cursor = "pointer";
	// // closeBtn.style.backgroundColor = "#fff";
	// // div.style.display = "flex";         // 启用弹性布局
	// // div.style.justifyContent = "flex-end";  // 右对齐
	// // div.style.alignItems = "center";    // 顶部对齐
	// div.innerHTML = `<button data-type='effect' style='padding: 2px 5px;cursor: pointer;user-select: none;'>×</button>`;
	// const button = div.querySelector("button[data-type='effect']");
	// button.style.position = "absolute";
	// button.style.right = "5px";
	// button.style.top = "2px";
	// button.addEventListener("click", function(e) {
	//   e.stopPropagation();
	//   if (div.parentNode) div.parentNode.removeChild(div);
	// });
	//
	//
	// range.insertNode(div);
	// // 整光标位置
	// const newRange = document.createRange();
	// newRange.setStartAfter(div);
	// newRange.collapse(false);
	// selection.removeAllRanges();
	// selection.addRange(newRange);
	var tipBox = document.createElement("div");
	tipBox.innerHTML = `<span class='change' data-id=${value_url}></span> <button data-type='effect' style='font-size:10px;'>×</button>`
	// tipBox.innerHTML = "<img> <button data-type='effect' style='font-size:10px;'></button>";
	// var img = tipBox.querySelector("img")
	// img = document.createElement('img');
	// img.src = 'http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D'; // 替换为实际路径
	// // img.alt = '动态插入的图片';
	// img.style.width = '10px';
	// img.style.height = '10px';
	// tipBox.setAttribute("data-id", id);
	tipBox.setAttribute("data-type", "number11");
	tipBox.setAttribute("class", "parent");
	tipBox.setAttribute("contentEditable", "false");
	tipBox.style.border = "1px solid #ddd";
	tipBox.style.backgroundColor = "#eff8f2";
	tipBox.style.padding = "5px";
	tipBox.style.width = '60px'
	// tipBox.style.height = '100px'
	//
	tipBox.style.display = "inline-block";
	tipBox.style.borderRadius = "6px";
	tipBox.style.marginLeft = "5px";
	tipBox.style.pointerEvents = "auto";
	// tipBox.className = 'create_background'
	// tipBox.style.backgroundImage = "url(@/assects/images/aiImages/jingpin.png)"
	// tipBox.style.backgroundSize = "100%";
	// tipBox.style.backgroundRepeat = "no-repeat";
	// tipBox.style.backgroundColor = '#f00
	tipBox.style.backgroundImage = `url(${musicIcon})`
	tipBox.style.backgroundSize = '20px 20px';
	tipBox.style.backgroundRepeat = "no-repeat";
	tipBox.style.backgroundPosition = "5px center";
	var closeBtn = tipBox.querySelector("button");
	closeBtn.innerText = "×";
	// closeBtn.style.backgroundImage = "url(/assets/btn_5.png)"
	// closeBtn.style.backgroundSize = "cover";
	// closeBtn.style.backgroundRepeat = "no-repeat";
	closeBtn.style.border = "none";
	closeBtn.style.padding = "4px";
	closeBtn.style.fontSize = "14px";
	closeBtn.style.cursor = "pointer";
	closeBtn.style.backgroundColor = "#fff";
	closeBtn.style.marginLeft = "24px";

	// let id = nanoid(4)
	// const span = document.createElement('span');
	// // // span.style.display = "none";
	// // // span.style.width = '50px'
	// // // console.log(item)
	// // // span.innerText = 'llll'
	// // span.className = id;
	// // // span.data-id = id;
	// span.setAttribute('data-name', id);
	// range.surroundContents(span);
	// selection.removeAllRanges();
	// span.appendChild(range.extractContents());
	// range.insertNode(span);
	// closeBtn.style.height = "20px";
	// closeBtn.style.width = "20px";
	closeBtn.addEventListener("click", function (e) {
		e.stopPropagation();



		// const prevElement = tipBox.previousSibling
		// // console.log('ppp',prevElement)
		// if (!prevElement) return;
		// // // 创建文档片段存储内容‌:ml-citation{ref="3,5" data="citationList"}
		// const fragment = document.createDocumentFragment();
		// while (prevElement.firstChild) {
		//   fragment.appendChild(prevElement.firstChild);
		// }
		// // // // 清除内联样式‌:ml-citation{ref="4" data="citationList"}
		// prevElement.className = '';
		// // // // 替换原元素为纯内容‌:ml-citation{ref="1,3" data="citationList"}
		// prevElement.parentNode.replaceChild(fragment, prevElement);
		// prevElement.remove()
		if (tipBox.parentNode) tipBox.parentNode.removeChild(tipBox);
	});

	tipBox.addEventListener("click", function (e) {
		e.stopPropagation();
		if (tipBox.getAttribute("data-type") === "number") {
			// openNumberPopup(tipBox);
		}
	});

	if (selection.rangeCount > 0) {
		range.collapse(false);
		range.insertNode(tipBox);
		// 获取 div 的父节点
		//     const parent = div.parentNode;
		// // 在父节点中，将 span 插入到 div 前面
		//     parent.insertBefore(span, div);
		console.log(tipBox)
	} else {
		ElMessage({
			message: '未检测到选中文本',
			type: 'warning',
		})
	}
	// 关闭弹窗
	// effects.value.effectsDialogVisible = false
	// stopPopover.value = false
	return "";
	// } else if (selectedText.length === 1 &&
	//     selectedText.charCodeAt(0) >= 0x4e00 &&
	//     selectedText.charCodeAt(0) <= 0x9fff) {
	//   return selectedText;
	// } else {
	//   alert("选中的内容必须是纯数字或单个汉字。");
	//   return "";
	// }
}








// 鼠标滑动选择文本时
const selectstart = (e) => { }
// // 粘贴处理
// const handlePaste = (e) => {
//   e.preventDefault()
//   const text = (e.clipboardData || window.clipboardData).getData('text')
//   const currentText = editorRef.value.innerText
//   const remainingSpace = MAX_LENGTH - currentText.length
//
//   if (remainingSpace <= 0) return
//
//   const pastedText = text.slice(0, remainingSpace)
//   document.execCommand('insertText', false, pastedText)
// }
//
// // 中文输入法处理
// const handleCompositionStart = () => {
//   isComposing.value = true
// }
//
// const handleCompositionEnd = (e) => {
//   isComposing.value = false
//   const newText = e.target.innerText
//   if (newText.length > MAX_LENGTH) {
//     editorRef.value.innerText = enforceLimit(newText)
//   }
// }
//
// // 替换功能示例
// const replaceText = (start, end, newText) => {
//   const current = editorRef.value.innerText
//   const modified = current.slice(0, start) + newText + current.slice(end)
//
//   if (modified.length > MAX_LENGTH) {
//     console.error('替换失败：超过字数限制')
//     return false
//   }
//
//   editorRef.value.innerText = modified
//   return true
// }
// let savedRange;
// const editable = ref(null);
// document.addEventListener('click', function(event) {
//   // Prevent default selection reset when clicking outside the contenteditable div
//   event.preventDefault();
//   if (savedRange) {
//     const selection = window.getSelection();
//     selection.removeAllRanges();
//     selection.addRange(savedRange);
//
//     setTimeout(()=>{
//
//       console.log('888888888888888',selection.getRangeAt(0).toString())
//     },1000)
//
//   }
// });

// 监听选区变化事件
// document.addEventListener('selectionchange', function() {
//   const selection = window.getSelection();
//
//   // var range = selection.getRangeAt(0);
//   // console.log('range.toString()',range.toString())
//
//   if (selection.toString()) {
//     selectedText = selection.toString();
//     console.log('更新选中内容:', selectedText);
//   }
// });
//
// // 监听点击事件，处理获取选中的内容
// document.addEventListener('click', function(event) {
//   setTimeout(function() {
//     const currentSelection = window.getSelection().toString();
//     if (!currentSelection) {
//       if (selectedText !== '') {
//         console.log('当前未选择文字，取最近一次选中内容:', selectedText);
//         // 在这里处理需要使用的选中的内容
//       } else {
//         console.log('没有任何选中内容');
//       }
//     } else {
//       console.log('点击后选区内容:', currentSelection);
//     }
//   }, 100); // 添加延迟以确保选区更新
// });




// 多人出样div变量
const sampleValue = ref(true)
const change_sample = (val) => {
	sampleValue.value = val
	soundList.value.map(item => {
		item.isSelected = false
	})
	if(ai_match_ref.value){
		ai_match_ref.value.chooseArr=[]
	}

}
// 顶部步骤条
const progress_barNum = ref(0)
const progress_bar_arr = ref([
	{ name: '文案', text: '输入或导入文案', isSelected: false },
	{ name: '样音', text: '单个或多人样音', isSelected: false },
	{ name: '成品', text: '合成成品', isSelected: false },
	{ name: '后期', text: '编辑修改', isSelected: false },
])
//点击步骤条0的时候中的合成样音按钮
const click_slide_button = (val, bool, param) => {
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
	// if(val!==2){
	//   progress_barNum.value = val
	// }
	syntheticAudioButton(1, bool, param)
}



// 操作多音字等按钮
const iconsArrLeft_four = reactive([
	// {IconName:'daoru',name:'导入文案'},
	// { IconName: 'duoyinzi', name: '多音字' },
	{ IconName: 'bieming', name: '读音替换' },
	{ IconName: 'charutingdun', name: '停顿' },
	{ IconName: 'shuzifuhao', name: '数字符号' },
	// {IconName:'beijingyinle',name:'背景音乐'},
	// {IconName:'yinxiao',name:'音效'},
	{ IconName: 'pinyin_line', name: '查看拼音' },
])

const click_sample_later_item = (e, index) => {
	showPopover.value = false
	closePopUP()
	switch (index) {
		// case 0:
		// 	//多音字操作  点击多音字弹出选择多音字弹窗
		// 	//点击多音字  根据多因去查询这个字的多有多音字 polyphonicList
		// 	gethomograph()
		// 	break;
		case 0:

			// 点击读音替换
			isSelectedAlias()

			break;
		case 1:

			// 停顿弹窗
			stopPopover.value = true
			e.stopPropagation();
			break;
		case 2:
			// 先判断是否是纯数字
			isNanFun()
			break;
		case 3:
			// effects.value.effectsDialogVisible = true
			pinyinFun()
			break;
		case 4:
			// 查看拼音

			break;
	}
}

// 监听
watch(() => progress_barNum.value, (newVal) => {
	console.log('777777777777777777', newVal)
	useCommerDubbing.progress_barNum = newVal
})

// 点击添加背景音乐
const add_background_music = () => {
	proxy.$musicmodal.open()
}
// 保存样音列表
const sample_Sound_List = ref([])

// 播放样音中的其中某一个
const stop_timbre_Two = (index) => {
	sample_Sound_List.value.map((item, idx) => {
		if (index == idx) {
			item.isPauseTtsAudio = false
		} else {
			item.isPauseTtsAudio = true
		}
	})
}
// 点击下载样音
const download_timbre = async(index,e) => {


	let txt=''
	sample_Sound_List.value[index].subtitle_json.map((item)=>{
		txt=txt+item.text
	})

	synthetic_button_type.value = ''
	// handleBatchCreateAPI()
	let audio_url=sample_Sound_List.value[index].audio_file
	let suffix='mp3'
	if(e.index==1){
		audio_url=await map_to_wav(audio_url)
		console.log(audio_url,666);

		suffix='wav'
	}
try {
  const link = document.createElement('a');
const filename = `${txt.slice(0, 5)}—${selected_timbre_arr.value[index].platformNickname}.${suffix}`
    .replace(/[\/\\:*?"<>|]/g, '_');
  fetch(audio_url)
	.then(response => response.blob()) // 获取二进制 Blob
	.then(blob => {
		const url = URL.createObjectURL(blob); // 创建本地 URL

		link.href = url;
		link.download = `${filename}`; // 设置下载文件名

		link.click(); // 触发下载

		// 1秒后释放 URL，避免内存泄漏
		setTimeout(() => {
			URL.revokeObjectURL(url);
		}, 1000);
	})
 link.style.display = 'none';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

} catch (error) {
  console.error('下载失败:', error);
}
}



// 第四步合成按钮
const press_button4_button_loading = ref(false)
const progress_barNum4_button_synthesis = () => {
	console.log(progress_barNum_3_result_oss_paths.value)
	full_loading.value=true
	press_button4_button_loading.value = true
	handle_merge_audio_mp3({
		oss_paths: progress_barNum_3_result_oss_paths.value,
		bgm_url: useCommerDubbing.bgmusic_url,
		bgm_vol: progress_barNum4_slideValue.value / 10
	}).then(async(res) => {
		console.log(res)
		const {
			code,
			data: {
				content: { result = null } = {},
				status_code
			} = {}
		} = res || {};
		if (result) {
			await fetchUserBenefits()
			progress_barNum_4audioUrl.value = result
			ElMessage({
				message: '合成成功',
				type: 'success',
			})
			// 合成成功后添加埋点
			commercialDubbingAnalytics.trackFinalProduct(SoundItem.value.voiceName);
			//如果是第四部合成成品需要走保存

			batch_save_progress(3)
			// progress_barNum.value = 0
			// progress_barNum_4result.value = result
		} else {
			progress_barNum_4audioUrl.value = ''
			ElMessage({
				message: '请重试',
				type: 'error',
			})
		}
		full_loading.value=false
		press_button4_button_loading.value = false

	}).catch(err => {
		full_loading.value=false
		press_button4_button_loading.value = false
		ElMessage({
			message: '请重试',
			type: 'error',
		})
		console.log(err)
	})
}

// 真人配音弹窗
const Real_voice_acting_dialog = ref(false)


const editorRef11 = ref(null)
const syntheticAudioButton_reload = async (type, bool, param, index) => {
	full_loading.value = true
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
	if (sample_Sound_List.value.length) {
		sample_Sound_List.value.map(item => {
			item.isPauseTtsAudio = true
		})
	}
	if (sampleValue.value == false && progress_barNum.value == 1) {
		useCommerDubbing.select_timbre_itemIndex = index
	}


	// 只要点击合成和试听按钮就全部停掉音色列表中播放
	stop_timbre_play()
	if (type == 1) {
		//   合成音频单人 1  true  2
		if (bool && param == 2) {
			// progress_barNum0_right_button.value = true
			loading.value = true
		} else {
			// loading.value = true
			progress_barNum0_right_button.value = true
		}
	} else {
		// 快速试听 2  false  1
		trial_listening.value = true
	}
	if (param == 3) {
		progress_barNum4_Loading.value = true
	}
	if (param == 5) {
		// progress1_loading.value = true
		if (index || index == 0) {
			sample_Sound_List.value[index].loading = true
			// debugger
		}
	}

	//只有多人出样的时候才需要过滤
	if (!sampleValue.value&&!route.query.batchId) {
		selected_timbre_arr.value = soundList.value.filter(item => item.isSelected)
		console.log('78888888', selected_timbre_arr.value)
		selected_timbre_filter_arr.value = []
		selected_timbre_filter_arr.value.length = 0
		console.log('selected_timbre_arr', selected_timbre_arr)
		selected_timbre_arr.value.map(item => {
			selected_timbre_filter_arr.value.push(item.voiceName)
		})
	}


	// // 通过CSS属性选择器定位目标元素‌:ml-citation{ref="3,4" data="citationList"}
	const container = document.querySelector('#editor11');  // 目标父级div
	// // 提取ID名称集合
	//   const elements_data_IdList = [];
	//   elements_data_Id.forEach(element => {
	//     // if (element['dataset']) elements_data_IdList.push(element['dataset'].name);  //
	//   });
	// 获取音频链接
	// const audio_list = []
	// const create_background_divs = editorRef.value.getElementsByClassName('create_background');
	// create_background_divs.forEach(element => {
	//   // if (element['dataset']) audio_list.push(element['dataset'].id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	// });
	const elementsWithId = container.querySelectorAll('[id]');  // 获取所有带id属性的子元素‌:ml-citation{ref="3,4" data="citationList"}
	// // 提取ID名称集合
	const idList = [];
	elementsWithId.forEach(element => {
		if (element.id) idList.push(element.id);  // 过滤空ID‌:ml-citation{ref="1,4" data="citationList"}
	});
	// const targets = editorRef.value.getElementsByClassName('data-shuzi');
	const changContents = editorRef11.value?.getElementsByClassName('change');
	// const nanoidArray = []
	// for(let i=0;i<idList.length;i++){
	//   nanoidArray.push(nanoid(4))
	// }
	let originalArr = []
	Array.from(elementsWithId).forEach((el, idx) => {
		originalArr.push({
			id: idList[idx],
			text: el.innerText,
		})
	});
	let alternativeArr = []
	if (changContents) {
		Array.from(changContents).forEach((el, idx) => {
			alternativeArr.push({
				id: idList[idx],
				text: el.innerText,
			})
		});
	}
	const originalDiv = document.getElementById('editor11');
	console.log('originalDiv', originalDiv)
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	if (alternativeArr.length > 0) {
		for (let j = 0; j < originalArr.length; j++) {
			processedHTML.value = getReplacedContent('editor11', alternativeArr[j].id, alternativeArr[j].text);
			if (parentDiv) {
				const elements = parentDiv.querySelectorAll('*');
				elements.forEach(element => {
					if (element.id === alternativeArr[j].id) {
						element.textContent = alternativeArr[j].text; // 替换文本
					}
				});
				// processedHTML.value = elements
				// const targetDivs = parentDiv.querySelectorAll("div[data-type]");
				// targetDivs.forEach(div => div.remove());
			}
		}
	}
	// for(let j=0;j<originalArr.length;j++){
	//   processedHTML.value = getReplacedContent('editor', alternativeArr[j].id, alternativeArr[j].text);
	//   if (parentDiv) {
	//     const elements = parentDiv.querySelectorAll('*');
	//     elements.forEach(element => {
	//         if (element.id === alternativeArr[j].id) {
	//           element.textContent = alternativeArr[j].text; // 替换文本
	//         }
	//     });
	//     // processedHTML.value = elements
	//     // const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	//     // targetDivs.forEach(div => div.remove());
	//   }
	// }
	const elements = parentDiv.querySelectorAll("[data-type='number11']");
	const parentElement = document.getElementsByClassName('parent')
	const dataIdValues = [];
	Array.from(parentElement).forEach(parent => {
		const spans = parent.getElementsByTagName('span'); // 获取当前 parent 下的所有 span 元素
		Array.from(spans).forEach(span => {
			const dataId = span.getAttribute('data-id');
			// 如果 data-id 存在，则添加到数组中
			if (dataId) {
				dataIdValues.push(dataId);
			}
		});
		// const spans = parent.querySelectorAll('span');
		// // 遍历每个 span 元素
		// spans.forEach(span => {
		//   // 获取 span 元素的 data-id 属性值
		//   const dataId = span.getAttribute('data-id');
		//   // 如果 data-id 存在，则添加到数组中
		//   if (dataId) {
		//     dataIdValues.push(dataId);
		//   }
		// });
	});
	// 获取到的音效链接
	console.log('dataIdValues', dataIdValues)
	Array.from(elements).map((element, index) => {
		var linkElement = document.createElement('a');
		// linkElement.href = dataIdValues[index]; // 设置链接地址
		linkElement.textContent = '[sound:' + dataIdValues[index] + ']'; // 复制 div 的内容到链接
		// linkElement.style.backgroundColor = 'lightgreen'; // 设置链接背景颜色
		// element.textContent = dataIdValues[index]; // 替换内容
		// 用链接替换 div
		element.replaceWith(linkElement);
	})
	// const targetDivs = parentDiv.querySelectorAll('div:not([data-type="number11"])');
	const targetDivs = parentDiv.querySelectorAll("div[data-type]");
	targetDivs.forEach(div => div.remove());
	const targetDivs1 = parentDiv.querySelectorAll("button[data-type]");
	targetDivs1.forEach(div => div.remove());
	// console.log('opdsfi',parentDiv)
	// 需要传的字符串信息   注意  其中有数字就会转  需要处理
	textInfo = ''
	// if(progress_barNum.value==0){
	textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
		// console.log(parseInt(val))
		return `<#${parseInt(val) / 1000}#>`
	})
	// 把换行替换成字符串
	textInfo = textInfo.replace(/\n/g, '');
	// 保存未切割的字符串
	// useAIDubbing.textInfo = textInfo.replace(/\n/g, '');
	// }
	// // 步骤er种合成按钮
	// if(progress_barNum.value==1){
	//   textInfo = useAIDubbing.textInfo
	// }



	// sampleValue.value==false
	if (type == 2 || param == 2) {
		let parts = textInfo.split(/(\[sound:.*?\])+/);
		let finalText = parts.filter(part => part.trim() !== '')
		console.log('finalText', finalText)
		let length = 0
		for (let i = 0; i < finalText.length; i++) {
			// console.log(finalText[i])
			if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
				if (finalText[i].length <= 200) {
					length += finalText[i].length
				} else {
					finalText[i] = finalText[i].substring(0, 200 - length);
					// debugger
				}
			}
		}
		textInfo = finalText.join('')
	}

	if (!textInfo) {
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		loading.value = false
		progress_barNum0_right_button.value = false
		trial_listening.value = false
		return
	}
	// 判断是多人出样还是单人出样
	if (sampleValue.value) {
		if (!SoundItemId.value) {
			ElMessage({
				message: '请先选择声音',
				type: 'warning',
			})
			loading.value = false
			progress_barNum0_right_button.value = false
			trial_listening.value = false
			return
		}
	} else {
		if (selected_timbre_filter_arr.value.length == 0) {
			ElMessage({
				message: '请至少选择一个声音',
				type: 'warning',
			})
			loading.value = false
			progress_barNum0_right_button.value = false
			trial_listening.value = false
			return
		}
	}

	handle_selectedTextList = []
	selectedTextList.map(item => {
		handle_selectedTextList.push(item.name)
	})


	// 检查输入的文本是否包含敏感词
	const res = await chekSensitive_Api({
		txt: textInfo,
	})
	const {
		code,
		data: {
			content: { result = null } = {},
			status_code
		} = {}
	} = res || {};
	// console.log('result',res)
	if (result && result.length > 0) {
		full_loading.value=false
		ElMessageBox.confirm(
			`文本中包含${result.join('、')}敏感词，是否继续生成音频？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				// type: 'warning',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				showClose: false,
				beforeClose: (action, instance, done) => {
					if (action === "confirm") {
						instance.confirmButtonLoading = true;
						instance.confirmButtonLoading = false
						done()
						full_loading.value=true
						popUp_query_button_progress_barNum4(type, bool, param, index)
					} else {
						progress_barNum4_Loading.value = false
						cancel_check()
						done()
					}
				}

			}
		).then(res => {
			// console.log('77777777777')
		}).catch(err => {
			loading.value = false
			trial_listening.value = false
		})
	} else {
		//直接合成音频
		popUp_query_button_progress_barNum4(type, bool, param, index)
	}

	// useAIDubbing.textInfo = textInfo
}


const popUp_query_button_progress_barNum4 = (type, bool, param, index) => {
	console.log(type, bool, param, index,'popUp_query_button_progress_barNum4');
	let obj = {
		user_id: JSON.parse(localStorage.getItem('user'))?.userId || '',
		text: textInfo,
		audio_format: "mp3",
		chunk_size: 2048,
		speed: progress_barNum.value == 3 ? progress_barNum3_speechValue.value : speechValue.value, //语速
		vol: slideValue.value / 10, //音量
		pitch: progress_barNum.value == 3 ? progress_barNum3_intonationValue.value : intonationValue.value,  //语调
		pronunciation: handle_selectedTextList, //多音字列表
		model: 'speech_SFT_01',
		vol_main:volumeValue.value/100, //背景音量
		// bgm_url:useAIDubbing.bgmusic_url
	}
	if (sampleValue.value) {
		// 单人合成配音
		if (param == 2) {
			obj.voice_ids = [SoundItemId.value]
		} else {
			obj.voice_id = SoundItemId.value
		}
	} else {
		// console.log('selected_timbre_arr.value',selected_timbre_arr.value)
		// debugger

		if (param == 5) {
			obj.voice_id = selected_timbre_arr.value[index].voiceName
			// debugger
		} else if (param == 3) {
			console.log( selected_timbre_arr.value[useCommerDubbing.select_timbre_itemIndex],useCommerDubbing.select_timbre_itemIndex);

			obj.voice_id = selected_timbre_arr.value[useCommerDubbing.select_timbre_itemIndex].voiceName
		} else {
			// 多人
			obj.voice_ids = selected_timbre_filter_arr.value
		}
	}
	console.log(soundList.value,SoundItem.value,'走了吗');
	console.log(88,param);


	if (bool) {
		Multiple_speech(obj, param, index)

	} else {
		single_speech(obj, type, bool, param)
	}
}
let handlePopoverClick = (event) => {
	event.stopPropagation();
};

let handleClickOutside = (event) => {
	const popoverElement = document.querySelector('.pause-popover');
	if (popoverElement && !popoverElement.contains(event.target)) {
		console.log('stopPopover3');
		stopPopover.value = false;
	}
	if (event.button === 0) {
		menuVisible.value = false
	}
};
let targetElements = ref([]);
//声音商店选中自动滑动到指定位置
let scrollToElement = () => {
  nextTick(() => {
    let scrollbar = scroll.value;
    let target = targetElements.value.find(el => el && el.dataset.voiceName == SoundItemId.value);

    if (target && scrollbar) {
      // 计算目标元素相对于滚动容器的偏移
      let targetOffset = 0;
      let el = target;
      const container = scrollbar.$el;

      while (el && el !== container) {
        targetOffset += el.offsetTop;
        el = el.offsetParent;
      }

      // 滚动到目标位置
      scrollbar.scrollTo({ top: targetOffset, behavior: 'smooth' });
    }
  });
};
let setRef = (index) => {
	return (el) => {
		if (el) {
			targetElements.value[index] = el; // 将元素存储到数组中
		}
	};
};
//提示弹窗
let showAlertDialog = ref(false)
let alert_title = ref('')
let alert_message = ref('')
let alert_confirm_txt = ref('')
let alert_cancel_txt = ref('')
let alert_dialog_code = ref('')
let alert_dialog_code_arr = ref([301, 311, 313])
let alert_dialog_code_status = ref(0)
let show_alert_cancel_btn=ref(true)
//提示弹窗展示公共逻辑
let public_open_alert_dialog = (status,obj) => {
	let pack_check = packCheck(obj.voice_ids)
	if (route.query.package &&synthetic_button_type.value != 2&& !pack_check) {
		package_buy_dialog_ref.value.voiceName = obj.voice_id
		package_buy_dialog_ref.value.dialogVisible = true
		progress_barNum0_right_button.value = false
		return
	}
	show_alert_cancel_btn.value=true
	alert_dialog_code.value = status
	alert_dialog_code_status.value=''
	//试听
	if (synthetic_button_type.value == 2) {
		switch (status) {
			case 301:
			case 313:
				alert_title.value = '温馨提示'
				alert_message.value = '当前试听字符数不足，无法试听！'
				alert_confirm_txt.value = '确定'
				showAlertDialog.value = true;
				alert_dialog_code_status.value = 1
				show_alert_cancel_btn.value=false
				break;
			default:
				break;
		}

	} else {
		//合成
		switch (status) {
			case 311:
				alert_title.value = '温馨提示'
				alert_message.value = '当前至臻字符额度已用尽，或未购买该音色所属套餐包。'
				alert_confirm_txt.value = '购买至臻音色包'
				alert_cancel_txt.value = '暂不够买'
				showAlertDialog.value = true;
				alert_dialog_code_status.value = 2
				break;
			case 301:
			case 313:
					alert_title.value = '温馨提示'
					alert_message.value = '当前至臻字符额度已用尽！'
					alert_confirm_txt.value = '购买至臻音色包'
					alert_cancel_txt.value = '暂不够买'
					showAlertDialog.value = true;
					alert_dialog_code_status.value = 2
				break;

			default:
				break;
		}
	}
}
let handleOpenMember = () => {
	// 关闭弹窗
	showAlertDialog.value = false;
	let status = alert_dialog_code_status.value
	switch (status) {
		case 2:
			// router.push({ name: 'soundStore', query: { buy: true ,type: 'package',} });
			const url = `${window.location.origin}/soundStore?buy=true&type=package`;
			window.open(url, '_blank');
			break;
		default:
			break;
	}


}
const handleCloseLimitDialog = () => {
	// 关闭会员限制弹窗
	showAlertDialog.value = false;
};
let handleBatchCreateAPI = () => {
	let params = [{
		title: editorRef.value.textContent.slice(0, 10),
		copywriting: editorRef.value.innerHTML,
		type: "3",//type  类型（0:一键成片 1:AI配音 2:专业云剪 3:AI商配）
		userId: JSON.parse(localStorage.getItem('user'))?.userId || '', //用户id
		ossAudioKey: audioUrl.value,  //音频文件
		subTitleFile: captions_url.value,//字幕文件
		status: '1',
		heteronym: selectedTextList, //多音字列表
	}]
	if (synthetic_button_type.value == 2) {
		params.map((item) => {
			params[0].isTrialListen = true
		})
	}
	batchCreateAPI(params).then(res => {
	}).catch(err => {
		console.log(err)
		// ElMessage({
		// 	message: '操作失败，请稍后重试',
		// 	type: 'error'
		// })
	})
}
let contact_ref=ref(null)
let open_contact=()=>{
	contact_ref.value.dialogVisible=true
}
let showAudio=ref(true)//强制解决杨老师多个音频绑定同一个ref问题

let intonationSlider=ref(null)
let speechSlider = ref(null);
let volumeSlider = ref(null);
let intonationSlider1=ref(null)
let speechSlider1 = ref(null);
let volumeValue=ref(100)//试听和合成音量调节
let formatTooltipIntonation = (val) => val + '%';
let formatTooltipSpeech = (val) => val.toFixed(2) + 'x';
let formatTooltipVolume = (val) => val + '%';
let addUnitToInput=(inputEl, unit)=>{
  if (!inputEl) return;
  inputEl.classList.add('unit-input');
  inputEl.setAttribute('data-unit', unit);
}
// 输入时保持单位显示（防止被清除）
let onIntonationInput=(ref)=>{
  nextTick(() => {
    addUnitToInput(ref.$el.querySelector('.el-input__wrapper'), '%');
  });
}

let onSpeechInput=(ref)=>{
  nextTick(() => {
    addUnitToInput(ref.$el.querySelector('.el-input__wrapper'), 'x');
  });
}
let slider_input_init=()=>{
	nextTick(() => {
		addUnitToInput(intonationSlider.value.$el.querySelector('.el-input__wrapper'), '%');
		addUnitToInput(speechSlider.value.$el.querySelector('.el-input__wrapper'), 'x');
		addUnitToInput(volumeSlider.value.$el.querySelector('.el-input__wrapper'), '%');
		addUnitToInput(intonationSlider1.value.$el.querySelector('.el-input__wrapper'), '%');
		addUnitToInput(speechSlider1.value.$el.querySelector('.el-input__wrapper'), 'x');
	});
}
let getUserId = () => {
	return loginStore.userId || '11'
}
let getFileExtension=(url)=>{
	console.log(url,'getFileExtension');


  let urlWithoutParams = url.split('?')[0];
  let decodedUrl = decodeURIComponent(urlWithoutParams);
  let fileName = decodedUrl.substring(decodedUrl.lastIndexOf('/') + 1);
  let extension = fileName.substring(fileName.lastIndexOf('.') + 1);
  return extension;
}
let saveArr=ref([])
//样音 成品 后期保存到我的空间
let batch_save_progress=async(data)=>{
	console.log(data,'batch_save_progress');

	let params = {
		userId: getUserId()
	}
	let response =''
	if(!data){
		response = await getAlbumsByCreator(params)

	}


	let status=''
	switch (progress_barNum.value) {
		case 1:
			status=2
			break;
		case 2:
			status=3
			break;
		case 3:
			status=1
		default:
			break;
	}

	console.log(textLength,999);

	let arr=[]
	let arr1=[]
	if(progress_barNum.value==1){
		arr1=selected_timbre_arr.value
	}else{
		arr1=[SoundItem.value]
	}
	arr1.map((item,index)=>{
			arr.push({
				copywriting: editorRef.value.innerHTML,//文案具体内容
                type: "3",//type  类型（0:一键成片 1:AI配音 2:专业云剪 3:AI商配）
                userId: JSON.parse(localStorage.getItem('user'))?.userId || '', //用户id
				title: editorRef.value.textContent.slice(0, 10),
                status,//状态（0:草稿 1:完成 2样品 3成品）
               	videoId:item.id,//使用的音色id
				avatarUrl:item.avatarUrl,//头像
				platformNickName:item.platformNickname,//昵称
				contentLength:textLength.value,//字数/时长(单位:秒)
				ossAudioKey:'',//oss音频文件url
				audioName:'',//音频名称
				id:item.saveId,//我的空间id
				voiceName:item.voiceName,//音色名称
				detailJson:'',

				// contentLength:
			})
			if(progress_barNum.value==1){
				arr[index].ossVedioKey=sample_Sound_List.value[index].audio_file
				arr[index].fileExtension=getFileExtension(arr[index].ossVedioKey)//文件扩展名
				let detailJson = {
					sample_Sound_List:sample_Sound_List.value,
				}
				arr[index].detailJson=JSON.stringify(detailJson)
			}else if(progress_barNum.value==2){
				console.log('第三步');

				arr[index].ossVedioKey=progress_barNum_2_audioUrl.value//音色url
				//第二步点击编辑修改使用

				arr[index].detailJson=JSON.stringify({...progress_barNum_2_result.value,SoundItem:SoundItem.value})
				arr[index].fileExtension=getFileExtension(arr[index].ossVedioKey)//文件扩展名
				arr[index].id=saveId.value//我的空间id


			}else{
				console.log(SoundItem.value,'SoundItem.value');

				arr[index].platformNickName=SoundItem.value.platformNickname
				arr[index].videoId=SoundItem.value.id
				arr[index].voiceName=SoundItem.value.voiceName
				//第二部点击编辑修改进入第三步接口返回
				console.log(progress_barNum_3_result.value,useCommerDubbing,'progress_barNum_3_result' );
				arr[index].ossAudioKey = useCommerDubbing.bgmusic_url;
				arr[index].ossVedioKey = progress_barNum_3_result.value.audio_file;
				arr[index].fileExtension = getFileExtension(arr[index].ossVedioKey); // 文件扩展名
				arr[index].id = saveId.value; // 我的空间id

				// 使用 toRaw 获取 useCommerDubbing 的原始对象，避免循环引用
				let detailJson = {
					progress_barNum_3_result_subtitle_json:progress_barNum_3_result_subtitle_json.value,
					pinyinResult:pinyinResult.value,
					html:progress_barNum_3_result_subtitle_json.value[0].text,
					textInfo:textInfo,
					progress_barNum3_speechValue:progress_barNum3_speechValue.value,
					speechValue:speechValue.value,
					slideValue:slideValue.value,
					progress_barNum3_intonationValue:progress_barNum3_intonationValue.value,
					intonationValue:intonationValue.value,
					handle_selectedTextList:handle_selectedTextList,
					volumeValue:volumeValue.value,
					selected_timbre_arr:selected_timbre_arr.value,
					progress_barNum_3_result_oss_paths:progress_barNum_3_result_oss_paths.value,
					useCommerDubbing:toRaw(useCommerDubbing.$state),  // 关键改动
					progress_barNum4_slideValue: progress_barNum4_slideValue.value,
					progress_barNum_4audioUrl: progress_barNum_4audioUrl.value,
					isPauseTtsAudio:isPauseTtsAudio.value,
					sample_Sound_List:sample_Sound_List.value,
					SoundItem:SoundItem.value
				};
				console.log(detailJson,'detailJson');
				arr[index].detailJson=JSON.stringify(detailJson)

			}
		})
	saveArr.value=arr
	if(save_id.value!=''){
		arr.map((item)=>{
			item.id=save_id.value
		})
	}
	if(data==3){
		arr.map((item)=>{
			item.albumId=''
		})
		save_space(arr,'no_message')
	}else{


		save_space_dialog_ref.value.list=response
		save_space_dialog_ref.value.params=arr
		save_space_dialog_ref.value.dialogVisible=true
	}

}

let show_fold_list=ref(false)//是否折叠音色列表
let show_ai_match=ref(false)//是否展示ai智能匹配
let ai_match_ref=ref(null)//ai智能匹配
//点击展开音色列表
let fold_list_click=()=>{
	show_fold_list.value=false
	show_ai_match.value=true
	if(show_list_nav_current.value==1&&sortedArr2.value.length>0){
		nextTick(()=>{
			if(ai_match_ref.value){
				ai_match_ref.value.list=sortedArr2.value.slice(0,10)
			}
		})
	}
	// show_list_left.value=false
}
//音色列表悬浮按钮
let fold_list_btn_click=()=>{
	show_fold_list.value=true
	show_ai_match.value=false
	// show_list_left.value=false
}
let show_list_left=ref(false)//左侧折叠只有点击收起音色列表展示
let sortedArr2=ref([])
//点击ai智能匹配
let ai_match_click=async()=>{
	if(!loginStore.token){
		proxy.$modal.open('组合式标题')
		return
	}
	show_fold_list.value=true
	show_ai_match.value=true
	nextTick(async()=>{
	ai_match_ref.value.loading=true
	ai_match_ref.value.chooseArr=[]
	const originalDiv = document.getElementById('editor');
	if (!originalDiv) return null;
	const parentDiv = originalDiv.cloneNode(true);
	textInfo = ''
	if (progress_barNum.value == 0) {
		textInfo = parentDiv.innerText.replace(/(\d+)m/g, function (val) {
			// console.log(parseInt(val))
			return `<#${parseInt(val) / 1000}#>`
		})
		// 把换行替换成字符串
		textInfo = textInfo.replace(/\n/g, '');
		// 保存未切割的字符串
		useAIDubbing.textInfo = textInfo.replace(/\n/g, '');
	}

		let parts = textInfo.split(/(\[sound:.*?\])+/);
		let finalText = parts.filter(part => part.trim() !== '')
		console.log('finalText', finalText)
		let length = 0
		for (let i = 0; i < finalText.length; i++) {
			// console.log(finalText[i])
			if (!/\[sound:(.*?)\]/g.test(finalText[i])) {
				if (finalText[i].length <= 200) {
					length += finalText[i].length
				} else {
					finalText[i] = finalText[i].substring(0, 200 - length);
				}
			}
		}
		textInfo = finalText.join('')
	if(textInfo==''){
		ElMessage({
			message: '请先输入文本信息',
			type: 'warning',
		})
		ai_match_ref.value.loading=false
		return
	}
	await soundListFun()
	cancelNavRequest.value = axios.CancelToken.source();
	let apiCall =await selectVoiceByAI({userId: JSON.parse(localStorage.getItem('user'))?.userId || '',question:textInfo,cancelToken: cancelNavRequest.value.token })

	try {
	const res = await apiCall
	console.log(res,'res');

if(res?.code === 0 &&res?.data?.status_code==200&& res?.data?.content?.result.length>0){
		if(res?.data?.status_code!=200){
			ElMessage({
				message: '匹配失败，暂无相应音色',
				type: 'error',
			})
		}
		// 建立 id -> index 映射
		const idOrderMap = {}
		  // 建立 id -> match 值映射
  		const idToMatchMap = {}
		res.data.content.result.forEach(([id,matchValue ], index) => {
			idOrderMap[id] = index
			idToMatchMap[id] = matchValue
		})
		// 给 soundList 中对应对象添加 match 字段
		soundList.value.forEach(item => {
			if(idToMatchMap.hasOwnProperty(item.id)){
				item.match = idToMatchMap[item.id]/5*100
			} else {
			item.match = null // 或者不赋值，根据需求
			}
		})
		// 根据映射排序 arr2
		sortedArr2.value = soundList.value.slice().sort((a, b) => {
		const indexA = idOrderMap[a.id]
		const indexB = idOrderMap[b.id]

		// 如果某个 id 不在 arr1 中，放到后面
		if (indexA === undefined) return 1
		if (indexB === undefined) return -1

		return indexA - indexB
		})

		// show_list_left.value=false

			if(ai_match_ref.value){

				// setTimeout(() => {
					ai_match_ref.value.loading=false
					ai_match_ref.value.list=sortedArr2.value.slice(0,10)
				// }, 500);
			}

	}else{
		ElMessage({
			message: '匹配失败，暂无相应音色',
			type: 'error',
		})
		soundList.value = []
		originalSoundList.value = []
		ai_match_ref.value.loading=false
	}
	} catch (err) {
		console.log(err, 'err')
		soundList.value = []
		originalSoundList.value = []
		ai_match_ref.value.loading=false
	}
})
}
//点击折叠音色列表
let expand_list_click=()=>{
	show_fold_list.value=true
	// show_list_left.value=true
}
let save_space_dialog_ref=ref(null)//保存到我的空间弹窗
let save_id=ref('')
//保存到我的空间
let save_space=async(params,message)=>{
	let result=await batchSave(params)
	console.log(result,'result');

	save_space_dialog_ref.value.loading=false
	save_space_dialog_ref.value.close()
	save_id.value=result.data.workIds[0]
	if(message!='no_message'){
		if(result.code==0){

			ElMessage({
				message: '保存到我的空间成功',
				type: 'success',
			})
		}else{
			ElMessage({
				message: result.msg,
				type: 'error',
			})
		}
	}
}
let show_list_nav_current=ref(0)
let change_list_nav=(data)=>{
	console.log(data);
	show_list_nav_current.value=data
	soundList.value = []
	originalSoundList.value = []
	cancelNavRequest.value&&cancelNavRequest.value.cancel('请求已被取消');
	selectetMycurrent.value=''
	if(data==0){
		soundListFun()
	}else if(data==1){
		ai_match_click()
	}else if(data==2){
		if(!loginStore.token){
			proxy.$modal.open('组合式标题')
			soundList.value=[]
			return
		}
		selectetMyClick('收藏')
	}

}
let selectetMycurrent=ref('')
let selectetMyClick=async(data)=>{
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
	soundList.value = []
	originalSoundList.value = []
	selectetMycurrent.value = data

	// 根据 data 选择对应的接口调用
	let apiCall
	cancelNavRequest.value = axios.CancelToken.source();
	if (data === '已购') {
		const userId = JSON.parse(localStorage.getItem('user'))?.userId || ''
		apiCall =await queryUserBuyVoiceName({ paymentType: "PURCHASE",tts: 5, userId ,cancelToken: cancelNavRequest.value.token})
	} else if (data === '收藏') {
		apiCall =await bookmarkList({ tts: 5, userId: JSON.parse(localStorage.getItem('user'))?.userId || '' ,cancelToken: cancelNavRequest.value.token})
	}else {
		apiCall =await queryUserUsedVoiceName({ tts: 5 ,userId: JSON.parse(localStorage.getItem('user'))?.userId || '' ,cancelToken: cancelNavRequest.value.token})
	}

	try {
	const res = await apiCall
	console.log(res,'res');

	if(data != '收藏'&&res.code === 0 && res?.data?.content?.result.length>0){
		console.log(res.data.content.result,777);

		res.data.content.result.forEach(item => {
			item.isPlay = false
			item.isSelected = false
			Object.assign(item, item.info)
		})
		let data1=JSON.parse(JSON.stringify(keysToCamelCase(res.data.content.result)))
		if(data=='历史'){
			data1=data1.slice(0,10)
		}
		Object.assign(originalSoundList.value, data1)
	}else if (data == '收藏' && res.length > 0) {
		res.forEach(item => {
			item.isPlay = false
			item.isSelected = false
		})
		Object.assign(originalSoundList.value, JSON.parse(JSON.stringify(res)))
	} else {
		originalSoundList.value = []
	}
	// 处理音色列表，确保SVIP音色排在前面
	let processedData = JSON.parse(JSON.stringify(originalSoundList.value));
	allSoundList.value=JSON.parse(JSON.stringify(originalSoundList.value));
	// 使用更严格的排序逻辑
	processedData = sortVoicesByMembership(processedData);
	// 更新显示列表
	soundList.value = processedData;
	} catch (err) {
		console.log(err, 'err')
		soundList.value = []
		originalSoundList.value = []
	}

}
let toCamelCase=(str)=>{
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}
let keysToCamelCase=(obj)=>{
  if (Array.isArray(obj)) {
    return obj.map(v => keysToCamelCase(v))
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = toCamelCase(key)
      acc[camelKey] = keysToCamelCase(obj[key])
      return acc
    }, {})
  }
  return obj
}
let getvoiceName=(id)=>{

	let index=soundList.value.findIndex(item=>item.id==id)
	let obj={}
	if(index>=0){
		obj=soundList.value[index]
	}
	return obj
}
let saveId=ref('')//我的空间跳转再次点击保存传的参数
//获取我的空间跳转保存内容
let getSpaceSave=async()=>{
	if(route.query.batchId){
		switch (route.query.status) {
			case '1':
				progress_barNum.value=3
				break;
			case '2':
				progress_barNum.value=1
				break;
			case '3':
				progress_barNum.value=2
				break;
			default:
				break;
		}
		let result=await getListForSave({
			batchId:route.query.batchId,
			status:route.query.status,//状态（0:草稿 1:完成 2样品 3成品）
		})
		console.log(result,'result');
		if(result.code==0){
			let detailJson1=JSON.parse(result.data[0].detailJson)
			sample_Sound_List.value=detailJson1.sample_Sound_List
			result.data.map((item,index)=>{
				console.log(getvoiceName(item.vedioId),item.vedioId,soundList.value,'是啥');
				let the_data=getvoiceName(item.vedioId)
				selected_timbre_arr.value[index]={...the_data}
				selected_timbre_arr.value[index].avatarUrl=item.vedioPersonImg
				selected_timbre_arr.value[index].platformNickname=item.voicePerson
				selected_timbre_arr.value[index].isSelected=true
				selected_timbre_arr.value[index].saveId=item.id
				selected_timbre_filter_arr.value[index]=item.voiceName


			})

			editorRef.value.innerHTML=result.data[0].copywriting
			sampleValue.value=false
			showAudio.value=true
			if(progress_barNum.value==1){
				sampleValue.value=false
				console.log(selected_timbre_arr.value,999);
				textLength.value=result.data[0].contentLength
			}else if(progress_barNum.value==2){
				saveId.value=result.data[0].id
				// SoundItem.value.avatarUrl=result.data[0].vedioPersonImg
				// SoundItem.value.platformNickname=result.data[0].platformNickname
				
				progress_barNum_2_audioUrl.value=result.data[0].ossVedioKey
				isPauseTtsAudio.value=false
				let detailJson=JSON.parse(result.data[0].detailJson)
				
				SoundItem.value=detailJson.SoundItem
				delete(detailJson.SoundItem)
				progress_barNum_2_result.value=detailJson

				
				sample_Sound_List.value=[]
				useCommerDubbing.select_timbre_itemIndex=0
				textLength.value=result.data[0].contentLength
				console.log(result.data[0].contentLength,textLength.value,'contentLength');

			}else{
				saveId.value=result.data[0].id
				progress_barNum_3_result.value.audio_file=result.data[0].ossVedioKey
				isPauseTtsAudio.value = true
				let the_data=JSON.parse(result.data[0].detailJson)
				console.log(the_data,the_data.progress_barNum_3_result_subtitle_json,'the_data');

				progress_barNum_3_result_subtitle_json.value=the_data.progress_barNum_3_result_subtitle_json
				pinyinResult.value=the_data.pinyinResult
				console.log(progress_barNum_3_result_subtitle_json.value,'progress_barNum_3_result_subtitle_json');
				textInfo=the_data.textInfo
				progress_barNum3_speechValue.value=the_data.progress_barNum3_speechValue
				speechValue.value=the_data.slideValue
				slideValue.value=the_data.slideValue
				progress_barNum3_intonationValue.value=the_data.progress_barNum3_intonationValue
				intonationValue.value=the_data.intonationValue
				handle_selectedTextList=the_data.handle_selectedTextList
				volumeValue.value=the_data.volumeValue
				selected_timbre_arr.value=the_data.selected_timbre_arr
				progress_barNum_3_result_oss_paths.value=the_data.progress_barNum_3_result_oss_paths
				useCommerDubbing.bgmusic_url=the_data.useCommerDubbing.bgmusic_url
				useCommerDubbing.bgmusicObj=the_data.useCommerDubbing.bgmusicObj
				useCommerDubbing.select_timbre_itemIndex=0
				useCommerDubbing.progress_barNum=the_data.useCommerDubbing.progress_barNum
				progress_barNum4_slideValue.value=the_data.progress_barNum4_slideValue
				progress_barNum_4audioUrl.value=the_data.progress_barNum_4audioUrl
				SoundItem.value=the_data.SoundItem
				isPauseTtsAudio.value=the_data.isPauseTtsAudio
				textLength.value=result.data[0].contentLength
				change_progress_barNum3_item(0)
				play_audio_progress_barNum3(0)
				console.log(the_data.useCommerDubbing,'useCommerDubbing');

	// document.getElementById('editor11').innerHTML = progress_barNum_3_result_subtitle_json.value[index].text
	// progress_barNum3_speechValue.value=progress_barNum_3_result_subtitle_json.value[index].speed
	// progress_barNum3_intonationValue.value=progress_barNum_3_result_subtitle_json.value[index].pitch
			}
		}



	}
}
let speechVisible=ref(false)
let intonationVisible=ref(false)
let progress_barNum4_slideValue_close=()=>{
	is_show_volume.value=false
}

let is_show_volume1 = ref(false)
let volumeValueClose=()=>{
	is_show_volume1.value=false
}
let speechValueClose=()=>{
	speechVisible.value=false
}
let intonationValueClose=()=>{
	intonationVisible.value=false
}
let onAudioEnded=()=>{
	if(progress_barNum.value==3){
		progress_barNum_3_result_subtitle_json.value[current_index.value].isPlay = false
		audioRef.value.pause()
	}
}
let audioRefEnd=()=>{
	if (audioRef.value) {
    	audioRef.value.addEventListener('ended', onAudioEnded);
  }
}
//ai智能推荐选择
let choose_ai=(data)=>{
	selectSoundItem(data)
}
let ai_match_close=()=>{
	show_ai_match.value=false
	show_fold_list.value=false
}
let currentPopover = ref(null);
let showPopoverFn=(type)=>{
  currentPopover.value = type;
}

let closePopUP=(event)=>{
 figurePopover.value = false;
 aliasPopover.value = false;
}
//音色列表收藏
//音色列表收藏
let collectClick=async(item)=>{
	if(!loginStore.token){
        proxy.$modal.open('组合式标题')
        return
    }
    let status='',tip='',result=0
    if(item.bookmark==0){
        result=1
        status='success'
        tip='收藏成功'
    }else{
        result=0
        status='error'
        tip='取消收藏'
    }

    try {
        let data=await bookmarkToggle({voiceId:item.id,type:result,tts:5,userId:loginStore.userId})
        if(data.code==1){
            ElMessage({ message:data.msg , type: 'error' });
            return
        }
        item.bookmark=result
        ElMessage[status](tip);
    }catch (error) {
        console.log(error,'error');
        ElMessage({ message:'数据加载失败，请刷新页面重试' , type: 'error' });

    }
}
let hasImg = ref(false)
let getTextNodes=(node)=>{
  let textNodes = []
  if (node.nodeType === Node.TEXT_NODE) {
    textNodes.push(node)
  } else {
    node.childNodes.forEach(child => {
      textNodes = textNodes.concat(getTextNodes(child))
    })
  }
  return textNodes
}
let rightPos=ref(0)
let textAfterCaret=ref('')
let menuVisible=ref(false)
//点击右键展示试听菜单
let handleRightClick = (event) => {
  if(!hasImg.value) return
  if(progress_barNum.value == 0){
    event.preventDefault() // 阻止默认右键菜单
    menuVisible.value = true

    // 获取选中的文本
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      textAfterCaret.value = selection.toString()
    } else {
      textAfterCaret.value = ''
    }
    console.log('选中的文本:', textAfterCaret.value)

    // 你可以根据选中范围计算百分比等
    const editor = editorRef.value
    if (!editor) return

    let charIndex = 0
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      // 计算选区开始位置的字符索引
      const textNodes = getTextNodes(editor)
      let found = false
      for (const node of textNodes) {
        if (node === range.startContainer) {
          charIndex += range.startOffset
          found = true
          break
        } else {
          charIndex += node.textContent.length
        }
      }
    }
    const totalLength = editor.innerText.length
    rightPos.value = totalLength > 0 ? (charIndex / totalLength) * 100 : 0
  }
}
//右键试听
let right_listen=()=>{
	console.log(textAfterCaret,'textAfterCaret');
	menuVisible.value = false
	if(progress_barNum.value==0){
		if(textAfterCaret.value==''){
			ElMessage({
				message: '请先选择文本信息',
				type: 'warning',
			})
			return
		}
		syntheticAudioButton(3, false, 1)
	}

}
let menuRef = ref(null)
let  globalClickHandler = async(event) => {
	if (event.button === 2) return // 右键不处理
	await nextTick()
	const menuEl = menuRef?.value?.$el
	console.log(menuEl,'menuEl');

	if (menuEl && menuEl.contains(event.target)) {
	// 点击在弹窗内部，不关闭弹窗，允许按钮事件触发
	return
	}

	// 点击弹窗外部，关闭弹窗
	menuVisible.value = false
}
provide('sampleValue', sampleValue);
let full_loading=ref(false)//全屏loading
let full_loading_timer = null;//全屏loading定时器
let full_loading_active_index = ref(0);//全屏loading当前柱子
let ai_copywriting_create_ref = ref(null);//AI文案创作弹窗
let ai_copywriting_show = ref(false); // AI文案弹窗
let ai_copywriting_tempalte_more_ref = ref(null);// AI文案模板更多弹窗
let ai_copywriting_toolbar_input_ref = ref(null); // AI文案工具栏输入框
let show_suspended_toolbar=ref(true)//悬浮工具栏
let {
      ai_copywriting_list,
      ai_copywriting_toolbar_current,
      ai_copywriting_toolbar_current_obj,
      inputText,
      ai_copywriting,
      change_copywriting_toolbar,
      ai_copywriting_more,
      ai_copywriting_input,
      handleKeydown,
      handlePaste,
      handleCompositionStart,
      handleCompositionEnd,
      ai_copywriting_send_message,
	  stopWriting
    } = useAiCopywriting({
	 proxy,
      templateList,
	  hasImg,
	  ai_copywriting_show,
	  ai_copywriting_tempalte_more_ref,
	  ai_copywriting_create_ref,
	  ai_copywriting_toolbar_input_ref,
	  show_suspended_toolbar,
	  editorRef
    });//1.2文案创作相关方法
	//文案创作重新生成
	let createCopyWriting=(data)=>{
		ai_copywriting_send_message(ai_copywriting_create_ref.value,data)
	}
	//文案创作插入编译器
	let insert_input=(data)=>{
		console.log('插入');
		editorRef.value.insertAdjacentHTML('beforeend', data);
	}
	let recover_toolbar=()=>{
		 if (editorRef.value) {
		// 创建一个新的 input 事件
		const event = new Event('input', {
		bubbles: true,
		cancelable: true,
		});
		// 触发事件
		editorRef.value.dispatchEvent(event);
		show_suspended_toolbar.value=true
		}
	}
	let try_listen_status=ref('end')//设置试听播放状态
	//试听设置状态
	let change_play_status=(data)=>{
		console.log('试听状态',data);

		if(synthetic_button_type.value==2||synthetic_button_type.value==3){
			try_listen_status.value=data
		}
	}
	let AudioPlayerRef1=ref(null)
	//试听按钮控制
	let try_listen_change=()=>{
		AudioPlayerRef1.value.playAudio()
	}
	//试听暂停
	let try_listen_stop=()=>{
		AudioPlayerRef1.value.audio_stop()
	}
	let findReplacementVisible=ref(false)
	let multilingualVisible=ref(false)
	// 点击页面非指定class元素时关闭popover
	let useClickOutside=(visibleRef, excludeClassName)=>{
		const handler = (event) => {
			const target = event.target
			if (!target) return

			// 判断点击元素是否是排除的class或其子元素
			if (target.closest(`.${excludeClassName}`)) {
			// 点击的是排除区域，不关闭
			return
			}

			// 点击了其他地方，关闭popover
			visibleRef.value = false
		}

		onMounted(() => {
			document.addEventListener('click', handler)
		})

		onUnmounted(() => {
			document.removeEventListener('click', handler)
		})
	}
	useClickOutside(findReplacementVisible, 'find_replacement_popover')
	useClickOutside(multilingualVisible, 'multilingual_popover')
	useClickOutside(ai_copywriting_show,'ai_copywriting_toolbar')
	let allVisibles = {
		findReplacement: findReplacementVisible,
		multilingual: multilingualVisible,
	}
	let {
		matches,
		findText,
		currentIndex,
		clearHighlights,
		highlightMatches,
		updateCurrentHighlight,
		handleUpward,
		handleDownward,
		handleReplace,
		handleReplaceAll,
		handleFind,
		findReplaceInit
	} = useFindReplace(editorRef,findReplacementVisible)
	//查找替换弹窗
	let find_replacement=()=>{
		findReplaceInit()
		findReplacementVisible.value=true
	}
	//多语种弹窗
	let multilingual=()=>{
		multilingualVisible.value=true
	}
	let downlaodButtonArr=reactive([
		{ name: 'mp3格式' ,index:0}, { name: 'wav格式',index:1 },
	])
	//清空文本
   let clear_text=()=>{
	if (editorRef.value) {
		editorRef.value.innerText = '';
		hasImg.value=false
		textLength.value = 0
	}
   }
   let initialized = false;
     watch(() => loginStore.token, (newVal, oldVal) => {
      console.log('token变化:', newVal);
       if (!initialized) {
			initialized = true;
			return;  // 第一次执行跳过刷新
		}
		if (!newVal) {
			window.location.reload();
			useAIDubbing.bgmusic_url = ''
			useAIDubbing.bgmusicObj = {}
		}
    }, { immediate: true,deep:true });
	let cancel_check=()=>{
		full_loading.value=false
	}

let insertImageAtCursor=(src)=>{
  const sel = window.getSelection();
  if (!sel.rangeCount) return;

  const range = sel.getRangeAt(0);
  range.deleteContents();

  const img = document.createElement('img');
  img.src = src;
  img.style.maxWidth = '100%'; // 限制图片宽度，防止撑破布局

  range.insertNode(img);

  // 把光标移到图片后面
  range.setStartAfter(img);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
}
let insertTextAtCursor=(text)=>{
  const sel = window.getSelection();
  if (!sel.rangeCount) return;

  const range = sel.getRangeAt(0);
  range.deleteContents();

  const textNode = document.createTextNode(text);
  range.insertNode(textNode);

  range.setStartAfter(textNode);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
}
	// let isTooltipVisible = ref(true);
</script>

<template>
	<div class="main_content padding-n-30" id="app_out_in" @click="handleContainerClick">
		<!-- 导入文案弹窗 -->
		<GlobalimportLetter ref="Letter" @call-parent="handleCallLetter"></GlobalimportLetter>
		<!--  音效弹窗  -->
		<soundEffects ref="effects" @call-parent="handleCallParent"></soundEffects>
		<!-- 播放音色列表 -->
		<audio ref="audioRef">
			<source type="audio/mpeg">
		</audio>
		<!--    多音字选择弹窗div   -->
		<el-popover :visible="showPopover" popper-class="polyphonic"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:fit-content`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in polyphonicList" :key="index" @click="clickPolyphonic(item)">{{ item }}</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!--  停顿弹窗  -->
		<el-popover :show-arrow="false" :visible="stopPopover" popper-class="polyphonic pause-popover"
			@click="handlePopoverClick"
			:popper-style="`top:${topPopover}px!important;left:${lettPopover_pause}px!important;padding:5px;width:auto;height:fit-content`">
			<div class="flex" contenteditable="false">
				<div style="padding:3px 5px;background-color: #fff;border: 1px solid #e5e5e5;border-radius: 4px"
					class="polyphonic_item margin_r-8 cursor-pointer font-size-13"
					v-for="(item, index) in standstillList" :key="index" @click="clickStandstill(item)">{{ item }}ms
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>
		<!-- 数字符号弹窗  -->
		<el-popover :visible="figurePopover" popper-class="polyphonic figure-popover" @click="handlePopoverClick"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:auto;height:fit-content`">
			<div contenteditable="false">
				<div style="margin-bottom:4px;" class="polyphonic_item1 cursor-pointer font-size-13"
					v-for="(item, index) in figureList" :key="index" @click="clickFigure(item)">
					<div class="font-size-12 iii">
						<span class="margin_r-10">{{ item.title }}</span>
						<span>{{ item.num }}</span>
					</div>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>

		<!--  读音替换弹窗 -->
		<el-popover :visible="aliasPopover" popper-class="polyphonic alias-popover" @click="handlePopoverClick"
			:popper-style="`top:${topPopover}px!important;left:${leftPopover}px!important;padding:5px;width:180px;height:fit-content`">
			<div contenteditable="false">
				<el-input v-model="aliasValue" placeholder="请输入读音替换"></el-input>
				<div class="button_div flex flex_a_i-center margin_t-10">
					<el-button @click="addAlias" type="primary" size="default" style="margin:auto;">确定</el-button>
				</div>
			</div>
			<template #reference>
				<span></span>
			</template>
		</el-popover>

		<div class="main_content_top flex flex_a_i-center">
			<!--   顶部tab展示   -->
			<div class="main_content_top_item flex flex_a_i-center cursor-pointer flex flex_a_i-center padding_l-18"
				:style="progress_barNum == 0 && index == 0 ? { backgroundImage: `url(${first_step_active})` } :
					progress_barNum == index && index != 0 ? { backgroundImage: `url(${other_step_active})` } :
						index == 0 ? { backgroundImage: `url(${firstStepBg})` } :
							{ backgroundImage: `url(${otherStepBg})` }" v-for="(item, index) in progress_bar_arr">
				<div v-show="!item.isSelected" class="main_content_top_item_left width-30 height-30 flex flex_a_i-center flex_j_c-center font-size-14
              margin_r-5" :class="{ 'is_active': progress_barNum == index }">
					{{ index + 1 }}
				</div>
				<img v-show="item.isSelected" src="@/assets/images/commercialImages/check_mark_icon.png"
					class="width-28 height-28 margin_r-6" alt="" srcset="">
				<div class="main_content_top_item_right flex flex_d-column">
					<span class="margin_b-4">{{ item.name }}</span>
					<span>{{ item.text }}</span>
				</div>
			</div>
			<el-button type="success" style="background-color: #0AAF60;" class="margin_l-16 font-size-14 width-100"
				@click="click_slide_button(1, true, 2)" v-show="progress_barNum == 0" :loading="loading">
				<template #icon>
					<img src="@/assets/images/commercialImages/yinpinIcon.png" alt="" srcset=""
						class="width-18 height-18">
				</template>
				合成样音
			</el-button>

			<el-button @click="syntheticAudioButton(1, false, 4)" :loading="progress_barNum0_right_button" type="success"
				class="margin_l-16 font-size-14 width-100" style="background-color: #fff;border:none;color:#0AAF60"
				v-show="progress_barNum == 0 && sampleValue">
				<template #icon>
					<Iconfont color="#0AAF60" size="20px" name="yinle" />
				</template>
				合成成品
			</el-button>


			<!--      <el-button style="background-color: #fff;border: none;color: #0AAF60" class="font-size-14 width-100" v-show="progress_barNum==1" @click="click_slide_button(2)">-->
			<!--        完成-->
			<!--      </el-button>-->

			<!--      <el-button style="background-color: #fff;border: none;color: #0AAF60" class="font-size-14 width-100" v-show="progress_barNum==2">-->
			<!--        完成-->
			<!--      </el-button>-->

			<!--      <el-button style="background-color: #fff;border: none;color: #0AAF60" class="font-size-14 width-100" v-show="progress_barNum==3">-->
			<!--        确定-->
			<!--      </el-button>-->
			<el-button type="primary" class="margin_l-16 font-size-14 width-100 main_content_top_save"
				v-show="progress_barNum != 0" @click="batch_save_progress()">
				完成
			</el-button>
			<el-button type="success" class="margin_l-16 font-size-14 width-100" @click="open_contact"
				style="border:none;background: linear-gradient( 134deg, #0AAF60 0%, #FFD600 100%);"
				v-show="progress_barNum == 3">
				<template #icon>
					<img src="@/assets/images/commercialImages/button_icon.png" alt="" srcset=""
						class="width-18 height-18">
				</template>
				真人配音
			</el-button>


			<!--      <div class="main_content_top_item flex flex_a_i-center flex_j_c-center margin_r-10 cursor-pointer"-->
			<!--           v-for="(item,index) in downlaodButtonArr" :key="index" @click.stop="clickDownlaodButton($event,index)">-->
			<!--        <Iconfont-->
			<!--            class="margin_r-4"-->
			<!--            size="16px"-->
			<!--            :name="`${item.url}`"-->
			<!--        />-->
			<!--        <span>{{item.name}}</span>-->
			<!--      </div>-->
		</div>


		<!--  真人配音弹窗  -->
		<!-- <el-dialog v-model="Real_voice_acting_dialog" width="30%">
			<img src="@/assets/images/commercialImages/Group91.png" alt="" style="width: 100%;height:300px">
		</el-dialog> -->
		<contact ref="contact_ref"></contact>
		<div class="main_content_bottom flex" v-show="progress_barNum == 0">
			<!--    中间左边输入文案  -->
			<div class="width-full height-full main_content_bottom_item_left margin_r-30  flex flex_d-column padding-24 "
				style="padding-bottom: 6px">
				<!--    输入文案顶部操作图标      -->
				<div class="main_content_bottom_item_left_top flex flex_j_c-space-between flex_a_i-center"
					contenteditable="false">
					<div class="main_content_bottom_item_left_top_left flex flex_a_i-center">
						<!-- ai智能匹配 -->
						<!-- <div class="ai_Intelligent_matching cursor-pointer" @click="ai_match_click">
							<img src="@/assets/images/aiImages/ai_Intelligent_matching.svg" alt="">
							<img class="ai_Intelligent_matching_icon" src="@/assets/images/aiImages/ai_Intelligent_matching_new.svg" alt="">
						</div> -->
						<div class="main_content_bottom_item_left_top_left_item flex flex_d-column flex_j_c-center flex_a_i-center cursor-pointer  margin_r-21 "
							v-for="(item, index) in iconsArr" :key="index" @click="clickIcons($event, index)"
							:data-id="`parent-${index}`"  :class="popupAddClass(item.name,item.className)"      ref="iconRefs">
							<template v-if="item.needFixed">
								<el-popover :visible="allVisibles[item.key].value" append-to="#app"
									 :popper-class="item.className ? `${item.className} action_bar_popover` : 'action_bar_popover'"
									 width="fit-content"
									:popper-options="{
										modifiers: [
										{
											name: 'offset',
											options: {
											offset: [205, 13] // [skidding, distance]
											}
										}
										]
									}"
								>
										<template #default>
											<findReplacement v-show="findReplacementVisible"
											  ref="findReplaceRef"
												:total="matches.length"
												:current="currentIndex + 1"
												@find="handleFind"
												@upward="handleUpward"
												@downward="handleDownward"
												@replace="handleReplace"
												@replaceAll="handleReplaceAll"
											 />
											<multilingualPopup v-if="multilingualVisible"/>
										</template>
										<template #reference>

											<div class="flex flex_j_c-center flex_d-column flex_a_i-center">
												<div class="main_content_bottom_item_left_top_left_item_img">
													<img :src="item.IconName" alt="">
												</div>
												<span class="font-size-12 main_content_bottom_item_left_top_left_item_span">{{ item.name }}</span>
											</div>

										</template>
								</el-popover>
							</template>
							<template v-else>
								<div class="main_content_bottom_item_left_top_left_item_img">
									<img :src="item.IconName" alt="">
								</div>
								<span class="font-size-12 main_content_bottom_item_left_top_left_item_span">{{ item.name }}</span>
							</template>
						</div>
						<!--      多人出样      -->
						<div class="flex flex_d-column flex_j_c-center flex_a_i-center cursor-pointer margin_r-30"
							v-show="sampleValue" @click="change_sample(false)">
							<!--              <Iconfont-->
							<!--                  class="margin_b-6"-->
							<!--                  size="26px"-->
							<!--                  name="renyuan"-->
							<!--              />-->
							<img src="@/assets/images/commercialImages/MultipleIcon.svg" alt="" srcset=""
								class="width-30 height-30 margin_b-4">
							<span class="font-size-14">多人出样</span>
						</div>
						<!--            -->
						<div class="main_content_bottom_item_left_top_left_item width-200 height-60 flex flex_a_i-center"
							v-show="!sampleValue">
							<Iconfont class="margin-n-8 iconPosition cursor-pointer" size="22px" color="#a0a2a5"
								name="chahao" @click="change_sample(true)" />
							<!--                <Iconfont-->
							<!--                    class="margin-n-8"-->
							<!--                    size="22px"-->
							<!--                    name="renyuan"-->
							<!--                />-->
							<img src="@/assets/images/commercialImages/MultipleIcon.svg" alt="" srcset=""
								class="width-30 height-30 margin-n-8">
							<div class="main_content_bottom_item_left_top_left_item_right flex flex_d-column">
								<span class="margin_b-6">多人出样模式</span>
								<span>可多选样音同时出样</span>
							</div>
						</div>
					</div>

					<div class="main_content_bottom_item_left_top_right width-200 height-40 font-size-12 padding-10 flex flex_a_i-center flex_j_c-space-between"
						v-if="useAIDubbing.bgmusic_url">
						<!--    叉号按钮      -->
						<Iconfont color="#959698" class="cursor-pointer position-icon" size="16px" name="chahao"
							@click="close_music_div" />
						<div class="main_content_bottom_item_left_top_right_left flex flex_a_i-center">
							<!-- <img src="@/assets/images/aiImages/yinyueIcon.png" alt="" style="width: 30px;height: 30px">-->
							<Iconfont class="cursor-pointer margin_r-5" color="#14b265" size="24px" name="yinle" />
							<div
								class="main_content_bottom_item_left_top_right_left_right width-130 height-full font-size-14 flex flex_d-column flex_j_c-center">
								<div class="margin_b-5">{{ useAIDubbing.bgmusicObj?.materialName ||
									useAIDubbing.bgmusicObj?.musicName }}</div>
								<!--              <div class="font-size-12">444</div>-->
							</div>
						</div>
						<div class="main_content_bottom_item_left_top_right_right" @click="click_volume">
							<Iconfont class="margin_r-8 cursor-pointer" size="20px" name="shengyin_shiti" />
						</div>
					</div>
					<el-tooltip class="box-item" effect="dark" content="展开音色" placement="bottom" v-if="show_fold_list&&!show_ai_match">
						<img src="@/assets/images/aiImages/fold_list.svg" class="fold_list cursor-pointer"  @click="fold_list_click" alt="">
					</el-tooltip>
				</div>
				<!--    输入文案div      -->
				<div class="main_content_bottom_item_left_bottom">
					<el-scrollbar>

						<!--   显示字数/清空文本     -->
						<div id="charCount" class="main_content_bottom_untill font-size-12">
							<div class="number-words main_content_bottom_untill_item">
								<span>{{ textLength }}</span>/{{ MAX_LENGTH }}
							</div>
							<div class="clear_text main_content_bottom_untill_item" v-if="hasImg" @click="clear_text">
								<img src="@/assets/images/aiImages/main_content_bottom_untill_clear_text.svg" alt="">清空文本
							</div>
						</div>
						<!--   输入文案div     -->
						<div v-show="pinyinResult.length > 0 && pinyinBool" class="copywriting-div padding-20">
							<div v-for="item in pinyinResult" :key="item.character"
								style="display: inline-block; text-align: center; margin: 5px;">
								<div style="font-size: 14px; color: #303133;font-weight: 500">{{ item.pinyin }}</div>
								<div style="font-size: 14px;font-weight: 500">{{ item.character }}</div>
							</div>
						</div>
						<!--           @selectstart="selectstart"  @keyup="handleKeyUp"-->
						<div v-show="!pinyinBool" ref="editorRef" contenteditable="true" @input="handleInput"
							id="editor" data-id="`parent-10`"
							style="line-height: 22px;letter-spacing: 2px;font-weight:500; "
							:style="{'padding-bottom':ai_copywriting_show?'136px':'45px'}"
							class="copywriting-div dzm-textarea padding-25 font-size-14"
							placeholder="粘贴或输入文本内容，限5000个字内" @mouseup="mouseup" @click="handleClick" @contextmenu="handleRightClick">
						</div>
						<!--                      <div class="AICopywriting_div width-440 height-90 margin_l-30 flex flex_d-column flex_a_i-center flex_j_c-space-between">-->
						<!--                        <div class="AICopywriting_div_top height-40 flex flex_a_i-center padding-n-10">-->
						<!--                          <img src="@/assets/images/aiImages/aiImgIcon.png" alt="" class="width-24 height-24">-->
						<!--                          <div class="width-1 height-16 margin-n-5"></div>-->
						<!--                          <el-input placeholder="输入您想要的文案类型" v-model="search_copywritingType" style="border:none"></el-input>-->
						<!--                        </div>-->
						<!--                        <div class="AICopywriting_div_bottom height-40 width-full flex flex_a_i-center flex_j_c-space-between">-->
						<!--                          <div class="AICopywriting_div_bottom_left height-full flex flex_a_i-center">-->
						<!--                            <div class="AICopywriting_div_bottom_left_item width-80 height-30 margin_r-10 flex flex_a_i-center flex_j_c-center cursor-pointer" v-for="item in 4">企业宣传</div>-->
						<!--                          </div>-->
						<!--                          <div class="AICopywriting_div_bottom_right height-full flex flex_a_i-center flex_j_c-flex-end font-size-14 cursor-pointer" @click="clickMoreIcon">-->
						<!--                            更多-->
						<!--                            <Iconfont-->
						<!--                                size="12px"-->
						<!--                                name="jiantou"-->
						<!--                            />-->
						<!--                          </div>-->
						<!--                        </div>-->
						<!--                      </div>-->

					</el-scrollbar>
						<!--   语速 语调 音量    -->
						<div class="speaker_content_bottom_left flex flex_a_i-center margin_r-20">
						<el-popover placement="top" :width="600" trigger="hover"
							popper-class="gradient-slider-popover intonation_popover" v-model:visible="intonationVisible">
							<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
								<el-slider  class="flex-item_f-8 slider gradient-slider"  @keydown.enter.prevent="intonationValueClose"
									v-model="intonationValue" :min="-12" :max="12" :step="1" show-input :format-tooltip="formatTooltipIntonation" ref="intonationSlider" :show-tooltip="false" @input="onIntonationInput(intonationSlider)"/>
							</div>
							<template #reference>
								<div
									class="speaker_content_bottom_left_left_button  height-30 flex flex_a_i-center padding-n-12 margin_r-12 cursor-pointer font-size-14">
									<span class="margin_r-12">语调</span>
									<span>{{ intonationValue }}%</span>
								</div>
							</template>
						</el-popover>
						<el-popover placement="top" :width="600" trigger="hover"
							popper-class="gradient-slider-popover speech_popover" v-model:visible="speechVisible">
							<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
								<el-slider  class="flex-item_f-8 slider gradient-slider" v-model="speechValue" @keydown.enter.prevent="speechValueClose"
									:min="0.5" :max="2.0" :step="0.01" :show-tooltip="false" show-input :format-tooltip="formatTooltipSpeech" ref="speechSlider" @input="onSpeechInput(speechSlider,speechValue,$event)"/>
							</div>
							<template #reference>
								<div
									class="speaker_content_bottom_left_left_button  height-30 flex flex_a_i-center padding-n-12 margin_r-12 cursor-pointer font-size-14">
									<span class="margin_r-12">语速</span>
									<span >{{ speechValue.toFixed(2) }}x</span>
								</div>
							</template>
						</el-popover>
						<!-- 音量 -->
						<el-popover
							placement="top"
							:width="600"
							trigger="hover"
							popper-class="gradient-slider-popover volume_popover"
							v-model:visible="is_show_volume1"
						>
							<div class="flex flex_a_i-center speaker_content_bottom_left_speed_speech">
							<el-slider
								v-model="volumeValue"
								:min="0"
								:max="1000"
								:step="1"
								show-input
								:show-tooltip="false"
								:format-tooltip="formatTooltipVolume"
								@keydown.enter.prevent="volumeValueClose"
								class="flex-item_f-8 slider gradient-slider"
								ref="volumeSlider"
							/>
							</div>
							<template #reference>
							<div class="speaker_content_bottom_left_left_button  height-30 flex flex_a_i-center padding-n-10 margin_r-10 cursor-pointer font-size-14">
								<span class="margin_r-12">音量</span>
								<span>{{ volumeValue }}%</span>
							</div>
							</template>
						</el-popover>
					</div>
					<!-- AI选音色悬浮 -->

					<!-- <img src="@/assets/images/aiImages/ai_choose_voice.png" class="ai_choose_voice cursor-pointer"   :style="{ top: aiPostion + 'px' }" v-if="hasImg"   @click="change_list_nav(1)" alt=""> -->
					 <!-- AI工具栏悬浮 -->
					 <!-- v-if="hasImg" -->
					<div class="ai_suspended_toolbar"  :style="{ top: aiPostion + 'px' }"  v-if="show_suspended_toolbar">
						<!-- <div class="ai_suspended_toolbar_item" @click="ai_copywriting"> -->
							<img src="@/assets/images/aiImages/ai_copywriting.svg" @click="ai_copywriting" alt="" >
						<!-- </div> -->
						<!-- <div class="ai_suspended_toolbar_item"  @click="change_list_nav(1)">
							<img src="@/assets/images/aiImages/ai_choose_voice.svg" alt="" >
							AI选音色
						</div> -->
					</div>
					<!-- AI文案工具栏悬浮 -->
					  <!-- v-if="ai_copywriting_show" -->
					<div class="ai_copywriting_toolbar"  :style="{ top: copywriting_aiPostion + 'px' }"  v-if="ai_copywriting_show">
						<div class="ai_copywriting_toolbar_top">
							<div class="ai_copywriting_toolbar_top_list">
								<div class="ai_copywriting_toolbar_top_list_item" v-for="item in ai_copywriting_list.slice(0,4)" :key="item.id" @click="change_copywriting_toolbar(item)" :class="ai_copywriting_toolbar_current==item.id?'active':''">
									{{ item.templateName }}
								</div>
							</div>
							<div class="ai_copywriting_toolbar_top_more"  @click="ai_copywriting_more">
								<span>更多模版</span>
								<img src="@/assets/images/aiImages/ai_copywriting_toolbar_top_more.svg">
							</div>
						</div>
						<div class="ai_copywriting_container">
							<!-- 高亮输入框，contenteditable 替换 el-input -->
							<img src="@/assets/images/aiImages/ai_copywriting_textarea_img.png" class="ai_copywriting_textarea_img" alt="">
							<div
								class="ai_copywriting_toolbar_input"
								contenteditable="true"
								ref="ai_copywriting_toolbar_input_ref"
								:placeholder="`请输入文案`"
								@input="ai_copywriting_input"
								@keydown.enter="handleKeydown($event, ai_copywriting_create_ref)"
								@paste="handlePaste"
								@compositionstart="handleCompositionStart"
								@compositionend="handleCompositionEnd"
							></div>
							<div class="ai_copywriting_toolbar_buttons">
								<div class="ai_copywriting_toolbar_group">
									<el-button @click="ai_copywriting_send_message(ai_copywriting_create_ref)" link class="ai_copywriting_toolbar_btn" circle>
										<img  src="@/assets/images/aiImages/ai_copywriting_toolbar_send.svg" alt="发送" />
									</el-button>
								</div>
							</div>
						</div>
					</div>
					<!--AI文案创作弹窗-->
					<aiCopyWritingCreate ref="ai_copywriting_create_ref" @createCopyWriting="createCopyWriting" @insert_input="insert_input" @stopWriting="stopWriting(ai_copywriting_create_ref)" @recover_toolbar="recover_toolbar"></aiCopyWritingCreate>
					<!--AI文案查看更多弹窗-->
					<aiCopyWritingTempalteMore ref="ai_copywriting_tempalte_more_ref" :ai_copywriting_create_ref="ai_copywriting_create_ref" @send_message="createCopyWriting"  @recover_toolbar="recover_toolbar"></aiCopyWritingTempalteMore>
				</div>
				<!--   底部播放按钮等   -->
				<div class="speaker_content_bottom height-60 flex flex_a_i-center flex_j_c-space-between">
					<!--    底部音频    -->
					<div class="speaker_content_bottom_bottom flex-item_f-2">
						<AudioPlayer ref="AudioPlayerRef1" :audioUrl="progress_barNum_0_audioUrl"  @change_play_status="change_play_status"
							:isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play" v-if="showAudio"></AudioPlayer>
					</div>
					<!--    中间 合成音频  快速试听    -->
					<div
						class="speaker_content_bottom_middle height-full flex flex_a_i-center flex_j_c-space-around padding_l-20">

						<!--            <el-tooltip-->
						<!--                class="box-item"-->
						<!--                effect="dark"-->
						<!--                content="合成将消耗字符数"-->
						<!--                placement="top-start"-->
						<!--            >-->
						<!--              <div class="speaker_content_bottom_middle_item_left flex flex_a_i-center flex_j_c-center cursor-pointer margin_r-10 height-40 width-100" @click="syntheticAudioButton(1)" v-loading="loading">-->
						<!--                <img src="@/assets/images/aiImages/synthetic_audio.png" alt="" class="width-18 height-18">-->
						<!--                <span class="span1 margin_l-5" >合成音频</span>-->
						<!--              </div>-->
						<!--            </el-tooltip>-->
						<div class="speaker_content_bottom_middle_item_right flex flex_a_i-center flex_j_c-center cursor-pointer"
							v-show="sampleValue"  v-loading="trial_listening">
							<template v-if="try_listen_status=='end'" >
								<div class="try_listen" @click="syntheticAudioButton(2, false, 1)">
									<span class="text-align-center">试听</span>
								</div>
							</template>
							<template v-else>
								<div class="try_listen">
									<el-tooltip effect="dark" :content="try_listen_status=='play'?'暂停':'播放'" placement="top" append-to="#app"  popper-class="try_listen-tooltip" >
										<div class="try_listen_item" @click="try_listen_change">
												<img src="@/assets/images/aiImages/try_listen_play.svg" alt="" v-if="try_listen_status=='play'">
												<img src="@/assets/images/aiImages/try_listen_pause.svg" alt="" v-else>

										</div>
									</el-tooltip>
									<el-tooltip  effect="dark" content="停止" placement="top" append-to="#app" popper-class="try_listen-tooltip" >
									<div class="try_listen_item"  @click="try_listen_stop">

											<img src="@/assets/images/aiImages/try_listen_stop.svg" alt="">

									</div>
									</el-tooltip>

								</div>
							</template>
						</div>
					</div>
				</div>
			</div>

			<!--    中间右边音色列表  -->
			<div class="width-full height-full main_content_bottom_item_right padding-24" v-if="!show_fold_list||show_ai_match">

				<!--      <el-scrollbar>-->
				<!--   顶部搜索   -->
				<div
					class="main_content_bottom_item_right_top_search flex flex_a_i-center flex_j_c-space-between margin_b-16 " :class="show_ai_match?'margin_b-24':''">
					<div class="main_content_bottom_item_right_top_search_left font-size-13">

						<!-- <img src="@/assets/images/aiImages/expand_list.svg" @click="expand_list_click" class="expand_list cursor-pointer"  alt="">  -->
						<span class="main_content_bottom_item_right_top_search_left_item margin_r-32 cursor-pointer" :class="show_list_nav_current==0?'current':''" @click="change_list_nav(0)">全部音色</span>
						<span class="main_content_bottom_item_right_top_search_left_item margin_r-32 cursor-pointer" :class="show_list_nav_current==1?'current':''" @click="change_list_nav(1)"><img src="@/assets/images/aiImages/ai_Intelligent_matching.png " alt="" class="width-60 height-20 "></span>
						<span class="main_content_bottom_item_right_top_search_left_item cursor-pointer" :class="show_list_nav_current==2?'current':''" @click="change_list_nav(2)">我的音色</span>
					</div>
					<div
						class="main_content_bottom_item_right_top_search_right flex flex_a_i-center flex_j_c-space-between">
						<img src="@/assets/images/aiImages/aidubbing_list_search.svg" @click="search_speaker" alt="">
						<el-input
							v-model="input_search"
							placeholder="输入想找的音色关键词"
							class="margin_r-10"
							@keyup.enter="search_speaker"
							@clear="handleClear"
							clearable>
						</el-input>
					</div>
				</div>
				<template v-if="show_list_nav_current!=1">
				<!--   选择项   -->
				<div class="speaker_content margin_b-24">
					<template v-if="show_list_nav_current==0">
					<!--      性别      -->
					<div class="speaker_content_list flex flex_j_c-flex-start">
						<div class="speaker_content_list_item margin_r-21 margin_b-10 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteGenderNum == item.name }" v-for="item in gendersArr"
							@click="selecteGender(item)">
							{{ item.name }}
						</div>
					</div>
					<!--     第二层数组     -->
					<!-- <div class="speaker_content_list flex flex_j_c-flex-start margin_b-6">
            <div class="speaker_content_list_item margin_r-20 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
                 :class="{'is_active':selecteSecondNum==item.name}" v-for="(item,index) in secondArr" @click="selecteSecond(item,index-1)">
              {{item.name}}
            </div>
          </div> -->
					<!--  第三层数组   uniqueList     -->
					<div class="speaker_content_list flex flex_j_c-flex-start">
						<div class="speaker_content_list_item margin_r-21 margin_b-10 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteUniqueNum == item.name }" v-for="item in thirdArrList"
							@click="selecteUnique(item)">
							{{ item.name }}
						</div>
					</div>
					<!--   第四层     -->
					<div class="speaker_content_list flex flex_j_c-flex-start">
						<div class="speaker_content_list_item margin_r-21 margin_b-10 10 padding-5 font-size-12 text-align-cetner cursor-pointer"
							:class="{ 'is_active': selecteEmotionNum == item.name }" v-for="item in emotionTagsArr"
							@click="selecteEmotion(item)">
							{{ item.name }}
						</div>
					</div>
					</template>
					<template v-else>
						<div class="speaker_content_list flex flex_j_c-flex-start ">
							<div class="speaker_content_list_item margin_r-13 10 margin_b-10 padding-5 font-size-12 text-align-cetner cursor-pointer"
								:class="{ 'is_active': selectetMycurrent == item.name }" v-for="item in myArrList"
								@click="selectetMyClick(item.name)">
								{{ item.name }}
							</div>
						</div>
					</template>
				</div>
				<!--  中间人物列表    -->
				<div class="speaker_content_middle  " v-loading="filter_listLoading" style="height:70%;">
					<el-scrollbar @scroll="handleScroll" ref="scroll" always>
						<div class="speaker_content_middle_list flex flex_j_c-flex-start" v-if="soundList.length > 0">
							<div class="speaker_content_middle_list_item width-115 height-151 flex flex_a_i-center  margin_r-13 margin_b-12 cursor-pointer  flex_j_c-center"
								v-for="(item, index) in soundList" :key="item.id" @click="selectSoundItem(item)"
								:ref="setRef(index)" :data-voice-name="item.voiceName"
								:style="item.isSelected ? `background-image:linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1))` : ''">
								<!-- 收藏 -->
								<div class="speaker_content_middle_list_item_collect">
									<img :src="item.bookmark==1 ? collectImage : collectNoImage" alt="" @click.stop="collectClick(item)">
								 </div>
								<!--       精品和珍享图片         -->
								<div class="position_image overflow-hidden">
									<!--                  {{item.isSelected}}-->
									<img :src="item.isSpecial ? jingpin : item.isTreasure ? zhenxiang : ''" alt="">
								</div>
								<!-- 音频 -->
								<div class="speaker_content_middle_list_item_voice">
								 </div>
								<!--       精品和珍享图片         -->
								<div class="position_image overflow-hidden">
									<!--                  {{item.isSelected}}-->
									<img :src="zhizhen" style="width: 55px;height: 18px" alt="">
								</div>
								<!--      心图片          -->
								<!--                <img src="@/assets/images/aiImages/collectIcon.png" alt="" class="collectIcon width-20 height-20">-->
								<div class="speaker_content_middle_list_item_insideDiv width-113 height-149 flex flex_d-column flex_a_i-center"
									>
									<!--    头像  名称     -->
									<div class="speaker_content_middle_list_item_insideDiv_avatar margin_t-30">
										<el-avatar :size="65" :src="item.avatarUrl" />

										<div class="speaker_content_middle_list_item_insideDiv_avatar_position_div  width-20 height-20 flex flex_a_i-center flex_j_c-center"
											v-show="!item.isPlay" @click="playAudio(item, index)">
											<Iconfont color="#fff" size="12px" name="bofang" />
										</div>

										<div class="speaker_content_middle_list_item_insideDiv_avatar_position_zanting  width-20 height-20 flex flex_a_i-center flex_j_c-center"
											v-show="item.isPlay" @click="playAudio(item, index)">
											<Iconfont color="#fff" size="12px" name="pause-fill" />
										</div>
									</div>
									<div
										class="speaker_content_middle_list_item_insideDiv_nickName margin_t-5 font-size-14"
										v-html="getDisplayName(item)">
									</div>
									<div
										class="speaker_content_middle_list_item_insideDiv_tags margin_t-2 font-size-12 color-gray"
										:style="item.voiceName == SoundItemId ? `color: #0AAF60;` : ''"
										v-html="getDisplayEmotionTag(item)"
										v-if="item.emotionTags || input_search.value">
									</div>
									<!--                  <div class="matching_degree margin_t-4">89%</div>-->
								</div>
							</div>
						</div>
						<el-empty description="暂无数据" v-else />
					</el-scrollbar>
				</div>
				</template>
				<!--ai智能匹配  -->
				<template v-else>
					<aiMatch ref="ai_match_ref" @choose_ai="choose_ai" @ai_match_close="ai_match_close"></aiMatch>
				</template>
				<!-- 折叠音色列表按钮	 -->

					<img src="@/assets/images/aiImages/fold_list_btn.svg" class="fold_list_btn cursor-pointer" @click="fold_list_btn_click" alt="">

			</div>
		</div>




		<!--  进度条等于1的时候显示  -->
		<div class="main_content_bottom1 " v-show="progress_barNum == 1">

			<el-scrollbar>
				<div class="main_content_bottom1_content">

					<div class="main_content_bottom1_content_item flex flex_a_i-center margin_b-16 overflow-hidden padding_r-20"
						v-for="(item, index) in sample_Sound_List" :key="item.trace_id">
						<div
							class="main_content_bottom1_content_item_left width-100 height-full flex flex_d-column flex_a_i-center flex_j_c-center">
							<img :src="zhizhen" class="position_img width-70 height-24">
							<el-avatar :size="50" :src="selected_timbre_arr[index].avatarUrl" class="margin_b-10" />
							<span>{{ selected_timbre_arr[index].platformNickname }}</span>
						</div>
						<div
							class="main_content_bottom1_content_item_right flex-item_f-1 flex flex_d-column height-full flex_j_c-space-between">
							<div class="main_content_bottom1_content_item_right_top width-full padding_t-30">
								<span v-for="(child, idx) in item.subtitle_json">{{ child.text }}</span>
							</div>
							<div
								class="main_content_bottom1_content_item_right_bottom height-60 flex flex_a_i-center flex_j_c-space-between">
								<div class="main_content_bottom1_content_item_right_bottom_left margin-10-n">
									<!--                <div class="main_content_bottom1_content_item_right_bottom_left_palyDiv flex flex_a_i-center">-->
									<!--                  <el-button type="primary" style="background-color: #0AAF60;border: none" size="small" circle>-->
									<!--                    <template #icon>-->
									<!--                      <Iconfont-->
									<!--                          color="#fff"-->
									<!--                          size="16px"-->
									<!--                          name="bofang"-->
									<!--                      />-->
									<!--                    </template>-->
									<!--                  </el-button>-->
									<!--                  <span class="margin_l-4">播放样音</span>-->
									<!--                </div>-->
									<!--                  <AudioPlayer ref="AudioPlayerRef" :audioUrl=" audioUrl" :isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play"></AudioPlayer>-->
									<AudioPlayer ref="AudioPlayerRef" :audioUrl="item.audio_file"
										:isPauseTtsAudio="item.isPauseTtsAudio" @stopPlay="stop_timbre_Two(index)" v-if="showAudio">
									</AudioPlayer>

								</div>
								<div class="main_content_bottom1_content_item_right_bottom_right">
									<el-dropdown placement="top" trigger="click" popper-class="aibubbing_download_menu">
										<el-button style="background: rgba(10,175,96,0.06);color:#0AAF60;border:none"
											class="font-size-12" >下载样音</el-button>
										<template #dropdown>
											<el-dropdown-menu>
												<template v-for="(item1, index1) in downlaodButtonArr" :key="index1">
														<el-dropdown-item @click.stop="download_timbre(index, item1)">
															{{ item1.name }}
														</el-dropdown-item>
												</template>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
									<el-button style="border-color:#0AAF60;color:#0AAF60" class="font-size-12 margin_l-12"
										:loading="item.loading"
										@click="syntheticAudioButton(1, false, 5, index)">合成成品</el-button>
								</div>
							</div>
						</div>
					</div>

				</div>

			</el-scrollbar>


		</div>

		<!--  进度条等于2的时候显示  -->
		<div class="main_content_bottom2 " v-show="progress_barNum == 2">
			<div class="main_content_bottom2_content">
				<div class="main_content_bottom2_content_item flex margin_b-16 overflow-hidden padding_r-20">
					<div
						class="main_content_bottom2_content_item_left width-100 height-full flex flex_d-column flex_a_i-center flex_j_c-center">
						<img :src="zhizhen" class="position_img width-70 height-24">
						<el-avatar :size="50" :src="SoundItem.avatarUrl" class="margin_b-10" />
						<span>{{ SoundItem.platformNickname }}</span>
					</div>
					<div
						class="main_content_bottom2_content_item_right flex-item_f-1 flex flex_d-column height-full flex_j_c-space-between">
						<div class="main_content_bottom2_content_item_right_top width-full padding_t-10">
							<div class="main_content_bottom2_content_item_right_top_content">
								<span
									v-for="(child, idx) in progress_barNum_2_result.subtitle_json">{{ child.text }}</span>
							</div>

						</div>
						<div
							class="main_content_bottom2_content_item_right_bottom height-45 flex flex_a_i-center flex_j_c-space-between margin-10-n">
							<div class="main_content_bottom2_content_item_right_bottom_left">
								<AudioPlayer ref="AudioPlayerRef" :audioUrl="progress_barNum_2_audioUrl"
									:isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play"  v-if="showAudio"></AudioPlayer>
							</div>
							<div class="main_content_bottom2_content_item_right_bottom_right">
								<el-dropdown placement="top" trigger="click" popper-class="aibubbing_download_menu">
									<el-button style="background: rgba(10,175,96,0.06);color:#0AAF60;border:none"
									class="font-size-12">下载成品</el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<template v-for="(item1, index1) in downlaodButtonArr" :key="index1">
													<el-dropdown-item @click.stop="download_mp3(item1)">
														{{ item1.name }}
													</el-dropdown-item>
											</template>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button style="border:none;color:#fff;background: #0AAF60;" class="font-size-12 margin_l-12"
									:loading="progress_barNum2_loading"
									@click="edit_timbre_progress_barNum2()">编辑修改</el-button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>


		<!--  进度条等于3的时候显示  -->
		<div class="main_content_sider_div flex" v-show="progress_barNum == 3">
			<!--  左边展示    -->
			<div class="main_content_sider_div_left width-full height-full margin_r-20 padding-20 flex flex_d-column">
				<!--   左边底部播放列表     -->
				<div class="main_content_sider_div_left_bottom">
					<el-scrollbar>
						<div class="main_content_sider_div_left_bottom_item width-full height-50 flex flex_a_i-center
                      flex_j_c-space-between padding-n-10 margin_b-12 cursor-pointer"
							@click="change_progress_barNum3_item(index)"
							:style="progress_barNum3_activeIndex == index ? { background: 'rgba(10,175,96,0.06)' } : { background: '#F7F7F9' }"
							v-for="(item, index) in progress_barNum_3_result_subtitle_json" :key="index">
							<div
								class="main_content_sider_div_left_bottom_item_left flex flex_a_i-center flex-item_f-1">
								<el-button type="primary" style="background-color: #0aaf60;border:none;" circle
									@click.stop="play_audio_progress_barNum3(index)">
									<template #icon>
										<Iconfont color="#fff" size="12px" :name="!item.isPlay ? 'bofang' : 'pause-fill'" />

									</template>
								</el-button>
								<span class="margin_l-10">{{ item.text }}</span>
							</div>
							<Iconfont @click.stop="progress_barNum3_download_text(index)" class="cursor-pointer"
								color="#0fb164" size="16px" name="xiazai1" />
						</div>
					</el-scrollbar>
				</div>
			</div>
			<!--  右边展示    -->
			<div class="main_content_sider_div_right width-full height-full flex flex_d-column"
				style="overflow: hidden;">
				<el-scrollbar class="flex-item_f-1 margin_b-20" style="background-color: #fff;border-radius: 8px;">
					<div class="main_content_sider_div_right_top margin_b-16  padding-20 flex-item_f-1">
						<div class="main_content_sider_div_right_top_top flex flex_j_c-space-between flex_a_i-center"
							contenteditable="false">
							<div class="main_content_sider_div_right_top_top_left flex">
								<div class="flex flex_a_i-center cursor-pointer margin_r-30"
									v-for="(item, index) in iconsArrLeft_four" :key="index"
									@click="click_sample_later_item($event, index)" :data-id="`parent-${index}`" :class="popupAddClass(item.name)">
									<Iconfont class="margin_b-2" size="18px" :name="`${item.IconName}`" />
									<span class="font-size-12 margin_l-4">{{ item.name }}</span>
								</div>
							</div>
						</div>
						<!--    编辑文字div      -->
						<div class="main_content_sider_div_right_top_div2 height-160 padding-10">
							<el-scrollbar>
								<div v-show="pinyinResult.length > 0 && pinyinBool" class="copywriting-div">
									<div v-for="item in pinyinResult" :key="item.character"
										style="display: inline-block; text-align: center; margin: 5px;">
										<div style="font-size: 14px; color: #303133;font-weight: 500">{{ item.pinyin }}
										</div>
										<div style="font-size: 14px;font-weight: 500">{{ item.character }}</div>
									</div>
								</div>
								<div v-show="!pinyinBool" ref="editorRef11" contenteditable="true" @input="handleInput"
									id="editor11"
									style="line-height: 22px;letter-spacing: 2px;font-weight:500;outline: none"
									:style="{'padding-bottom':ai_copywriting_show?'136px':'25px'}"
									data-id="`parent-10`" @mouseup="mouseup" @click="handleClick"  @contextmenu="handleRightClick">

								</div>
							</el-scrollbar>
						</div>

						<div class="main_content_sider_div_right_top_div4 flex flex_j_c-flex-end">
							<el-button style="background-color: #fff;border-color:#0AAF60;color:#0AAF60;font-size:12px;"
								:loading="progress_barNum4_Loading"
								@click="syntheticAudioButton_reload(1, false, 3)">重新合成</el-button>
						</div>
						<div class="main_content_sider_div_right_top_div1 flex flex_a_i-center margin_b-24">
							<img src="@/assets/images/commercialImages/divider.png" alt="" srcset=""
								class="width-4 height-18">
							<span class="font-size-15 margin_l-10">音频调节</span>
						</div>
						<div class="main_content_sider_div_right_top_div3 flex flex_a_i-center flex_j_c-space-between">
							<span
								class="main_content_sider_div_right_top_div3_span_first margin_r-36 text-align-center">语速</span>
							<el-slider class="flex-item_f-8 speech_slider" v-model="progress_barNum3_speechValue" ref="speechSlider1"
								:min="0.5" :max="2.0" :step="0.01" :show-tooltip="false" :format-tooltip="formatTooltipSpeech" @input="onSpeechInput(speechSlider1)" show-input/>
						</div>
						<div class="main_content_sider_div_right_top_div3 flex flex_a_i-center flex_j_c-space-between">
							<span
								class="main_content_sider_div_right_top_div3_span_first margin_r-36  text-align-center">语调</span>
							<el-slider class="flex-item_f-8 intonation_slider" v-model="progress_barNum3_intonationValue" ref="intonationSlider1" :format-tooltip="formatTooltipIntonation" @input="(val) =>onIntonationInput(intonationSlider1)"
								:min="-12" :max="12" :step="1" :show-tooltip="false" show-input />
						</div>
					</div>
				</el-scrollbar>






				<div class="main_content_sider_div_right_bottom padding-20">
					<div class="main_content_sider_div_right_bottom_div1 flex flex_a_i-center flex_j_c-space-between">
						<div class="main_content_sider_div_right_bottom_div1_left flex">
							<img src="@/assets/images/commercialImages/divider.png" alt="" srcset=""
								class="width-4 height-18">
							<span class="font-size-15 margin_l-10">成品试听</span>
						</div>

						<div class="main_content_sider_div_right_bottom_div1_right flex flex_a_i-center flex_j_c-space-between width-140 height-30 padding-n-6"
							v-if="useCommerDubbing.bgmusic_url">
							<Iconfont color="#959698" class="cursor-pointer position-icon1" size="16px" name="chahao"
								@click="close_music_div" />
							<div
								class="main_content_sider_div_right_bottom_div1_right_left flex flex_a_i-center height-full">
								<div class="position_volume width-40 height-160 flex flex_d-column flex_j_c-space-between flex_a_i-center padding-6-n"
									data-id="`parent-100`" v-show="is_show_volume">
									<div class="position_volume_span1 font-size-12">{{ progress_barNum4_slideValue }}%
									</div>
									<el-slider v-model="progress_barNum4_slideValue" vertical height="100px"
										size="small" :show-tooltip="false" @keydown.enter.prevent="progress_barNum4_slideValue_close"/>
									<div class="position_volume_span2 font-size-12">音量</div>
								</div>
								<Iconfont @click="click_volume" class="margin_r-8 cursor-pointer" size="20px"
									color="#fff" name="shengyin_shiti" />
								<span>{{ useCommerDubbing.bgmusicObj?.materialName ||
									useCommerDubbing.bgmusicObj?.musicName
									}}</span>
							</div>
							<div class="main_content_sider_div_right_bottom_div1_right_right cursor-pointer"
								@click="add_background_music">更换</div>
						</div>

						<el-button v-else
							style="background-color: #fff;border-color:#0AAF60;color:#0AAF60;font-size:12px;"
							@click="add_background_music">添加背景音乐</el-button>

					</div>
					<div class="main_content_sider_div_right_bottom_div2 flex-item_f-1 height-40 ">
						<AudioPlayer ref="AudioPlayerRef" :audioUrl="progress_barNum_4audioUrl"
							:isPauseTtsAudio="isPauseTtsAudio" @stopPlay="stop_timbre_play" style="width: 100%" v-if="showAudio">
						</AudioPlayer>
					</div>
					<div class="main_content_sider_div_right_bottom_div3">
						<div class="margin_b-10 flex flex_j_c-center" style="background: #0BAF60;border-radius: 4px">
							<el-button
								style="border: none;background: #0BAF60;color:#fff;height:28px;width: 110px; margin: auto;width:100%;"
								:loading="press_button4_button_loading" @click="progress_barNum4_button_synthesis">
								<template #icon>
									<Iconfont color="#fff" size="16px" name="yinle" />
								</template>
								合成成品
							</el-button>
						</div>
						<div style="background-color: rgba(10,175,96,0.06);border-radius: 4px"
							class="margin_b-10 flex flex_j_c-center" >
							<el-dropdown placement="top" trigger="click" popper-class="aibubbing_download_menu">
								<el-button
								style="height:28px;width: 90px;color:#0AAF60;border:none;background-color: rgba(255,255,255,0);"
								class="font-size-12">下载成品</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<template v-for="(item1, index1) in downlaodButtonArr" :key="index1">
												<el-dropdown-item @click.stop="download_mp4(item1)">
													{{ item1.name }}
												</el-dropdown-item>
										</template>
									</el-dropdown-menu>
								</template>
							</el-dropdown>

						</div>

					</div>
				</div>
			</div>
		</div>
	</div>
	<!--保存到我的空间弹窗 -->
	<saveSpaceDialog ref="save_space_dialog_ref" @save="save_space"></saveSpaceDialog>
	<packageBuyDialog ref="package_buy_dialog_ref"></packageBuyDialog>
	<AlertDialog v-model:visible="showAlertDialog" type="warning" :title="alert_title" :message="alert_message"
		:confirm-button-text="alert_confirm_txt" :cancel-button-text="alert_cancel_txt" :show-cancel-button="show_alert_cancel_btn"
		:custom-confirm-class="true" :custom-cancel-class="true" :show-fee-explanation="false"
		@confirm="handleOpenMember" @cancel="handleCloseLimitDialog" />
	<!-- 右键试听菜单 -->
	<el-dropdown placement="top-start" 	v-if="hasImg&&menuVisible"   ref="menuRef"		:style="{ position: 'fixed', top: `${topPopover}px`, left: `${leftPopover}px` }"
	>
	<el-button class="right_listen" @click="right_listen"><img src="@/assets/images/aiImages/right_listen.svg" alt=""> 试听 </el-button>
    </el-dropdown>
	<div class="full_screen_loading" v-if="full_loading">
		<div class="full_screen_loading_bar_boxs">
			<div
			v-for="(n, index) in 6"
			:key="index"
			:class="['full_screen_loading_bar', { active: index === full_loading_active_index }]"
			></div>
		</div>
		<span>正在{{synthetic_button_type==2||synthetic_button_type==3?'试听':'合成'}}音频，请稍后...</span>
	</div>
</template>

<style scoped lang="scss">
::v-deep(.el-scrollbar__bar.is-vertical div) {
	background-color: #0aaf60;
	/* 修改滚动条颜色 */
	opacity: 1;
}

.create_background {
	width: 20px;
	height: 20px;
	background-image: url("http://miaoyinbkt.oss-cn-wulanchabu.aliyuncs.com/material/%E5%A4%B4%E5%83%8F/%E8%87%BB%E4%BA%AB/%E7%B2%BE%E5%93%81-%E5%A5%B323-%E5%A5%B3%E7%AB%A5.png?Expires=1741528290&OSSAccessKeyId=LTAI5t6RFnCbx4x8GiUSvwK5&Signature=McSgap10Ugl54ldP%2FYVF%2FRmKtWM%3D");
}

//选中文字展示样式
.wenziDivStyle {
	border: none;
	background-color: #eff8f2;
	padding: 5px;
	display: inline-block;
	// tipBox.style.marginLeft = "5px";
	// tipBox.style.cursor = "pointer";
	// tipBox.style.pointerEvents = "auto";
}

::v-deep(.polyphonic_item1) {
	//background-color: #006eff;
	display: inline-block;
	width: 100%;
	height: 30px;
	line-height: 30px;

	.iii {
		width: 100%;
		height: 50%;
	}
}

.polyphonic_item1:hover {
	background-color: rgba(129, 107, 107, 0.21);
}



#editor {
	user-select: text;
	/* 确保文本可被选择 */
}

::v-deep(.tts-tag) {
	background-color: #f00 !important;
}


::v-deep(.el-popper__arrow::before) {
	//width: 0;
	//height:0;
	//border: none;
}

::v-deep(.el-popper__arrow::before) {
	/* position: absolute; */
	width: 0px;
	height: 0px;
	//z-index: -1;
	content: "";
	//transform: rotate(45deg);
	//background: var(--el-text-color-primary);
	box-sizing: border-box;
}

::v-deep(.el-popper.is-light, .el-popper.is-light>.el-popper__arrow):before {
	//background: var(--el-bg-color-overlay);
	//border: 0px solid var(--el-border-color-light);
}

::v-deep(.el-slider__button) {
	height: 14px;
	width: 14px;
}

::v-deep(.el-slider__bar) {
	//background: #0aaf60;
	// background: linear-gradient(108deg, #FFD600 0%, #0AAF60 100%);
}

::v-deep(.el-slider__button) {
	border: 2px solid #0aaf60;
}

.main_content {
	background-color: #f7f7f9;
	// overflow: hidden;
	position: relative;
	flex:1;
	height:100%;
	display:flex;
	flex-direction:column;
	box-sizing: border-box;
	min-height: 0;
	&_top {
		height: 90px;
		flex-shrink: 0;
		//background-color: #67c23a;
		&_save{
			color:#0AAF60 ;
			border: 1px solid #0AAF60;
			background-color: #fff;
		}
		&_item {
			width: 200px;
			height: 72px;
			flex-shrink: 0;
			//background-color: #f00;
			background-repeat: no-repeat;
			background-size: cover;

			//border-bottom:4px solid #fff;
			//text-align: center;
			//line-height: 30px;
			//border-radius:4px;
			//color: #121212;
			//background-color: #fff;
			//font-size: 12px;
			&_left {
				background: #F7F7F9;
				border: 2px solid #979797;
				border-radius: 50%;
				font-weight: 600;
			}

			.is_active {
				background-color: #0AAF60;
				color: #fff;
				border: 2px solid #0AAF60;
			}

			&_right {
				span {
					&:first-child {
						font-weight: 500;
						font-size: 14px;
						color: #121212;
					}

					&:last-child {
						font-weight: 400;
						font-size: 12px;
						color: #7E838D;
					}
				}
			}



		}


		//&_item{
		//  width:100px;
		//  height: 30px;
		//  text-align: center;
		//  line-height: 30px;
		//  border-radius:4px;
		//  color: #121212;
		//  background-color: #fff;
		//  font-size: 12px;
		//}
	}

	&_bottom {
		flex:1;
		min-height: 0;
		box-sizing: border-box;
		&_item_left {
			background-color: #fff;
			flex: 1;
			min-width: 650px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			//左边div顶部
			&_top {
				height: 70px;

				//background-color: #006eff;
				&_left {
					.main_content_bottom_item_left_top_left_item{
						position: relative;
						padding: 2px 3px;
					.main_content_bottom_item_left_top_left_item_img {
							width: 20px;
							height: 20px;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 6px;
							// margin-right: 6px;
							img {
								width: 20px;
								height: 20px;
							}

							.iconfont {
								margin-bottom: 0;
							}
					}
					.main_content_bottom_item_left_top_left_item_span{
						font-size: 14px;
						color: #111419;
						line-height: 22px;
						text-align: center;
						display: inline-block;
						min-width: 2em;
					}
					&:hover{
						background-color: #F7F7F9;
						border-radius: 3px;
					}
					}
					.ai_Intelligent_matching{
						position: relative;
						width: 70px;
						margin-right: 24px;
						align-self:flex-end;
						display: flex;
						height: 63px;
						align-items: flex-end;
						.ai_Intelligent_matching_icon{
							position: absolute;
							right: -15px;
							top: -9px;
							z-index: 1;
						}
					}
					.main_content_bottom_item_left_top_left_item {
						// background: #F7F7F9;
						// border-radius: 8px;
						// position: relative;

						.iconPosition {
							position: absolute;
							top: -7px;
							right: -15px;
						}

						&_right {
							span {
								&:first-child {
									font-weight: 500;
									font-size: 14px;
									color: #121212;
								}

								&:last-child {
									font-weight: 400;
									font-size: 12px;
									color: #7E838D;
								}
							}
						}
					}

				}

				&_right {
					background-image: url("@/assets/images/aiImages/top_right_bg.png");
					background-repeat: no-repeat;
					background-size: cover;
					position: relative;

					.position-icon {
						position: absolute;
						top: -7px;
						right: -7px;
					}

					&_left {
						&_right {
							div {
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;

								&:first-child {
									color: #121212;
								}

								&:last-child {
									color: #7E838D;
								}
							}
						}
					}

					&_right {}
				}
				.fold_list{
					width: 43px;
					height: 42px;
				}
			}

			//输入文案div样式
			&_bottom {
				flex: 1;
				background-color: #f7f7f9;
				border-radius: 8px;
				position: relative;
				outline: none;
				//overflow-x:hidden;
				overflow-y: hidden;
				position: relative;
				padding-bottom: 50px;
				.el-scrollbar{
					position: static;
					::v-deep(.el-scrollbar__view){
						height: 100%;
						overflow-y: auto;
					}
				}
				//多音字弹窗样式
				//弹窗样式
				//.popupStyle {
				//  :v-deep(.el-popper){
				//
				//    //top: 200px!important;
				//    //left: 200px!important;
				//  }
				//
				//}
				.position_volume {
					position: absolute;
					top: 20px;
					right: 20px;
					background-color: #fff;
					border-radius: 6px;

					&_span1 {}

					// 控制滑块的默认颜色
					::v-deep(.el-slider__bar) {
						background: #0aaf60;
					}

					::v-deep(.el-slider__button) {
						border: 2px solid #0aaf60;
					}

					&_span2 {}
				}
				//输入字数样式
				.main_content_bottom_untill {
					position: absolute;
					bottom: 17px;
					right: 12px;
					color: rgba(0,0,0,0.45);
					display: flex;
					align-items: center;
					font-size: 12px;
					line-height: 14px;
					height: 18px;
					.main_content_bottom_untill_item{
						position: relative;
						padding:0 12px;
						display: flex;
						align-items: center;
						&::after{
							content:'';
							position: absolute;
							width: 1px;
							height: 12px;
							background: #E4E9ED;
							right: 0;
							top: 50%;
							transform: translateY(-50%);
						}
						&.number-words{
							span{
								color: #368DFF;
							}
						}
						&.clear_text{
							font-size: 12px;
							line-height: 14px;
							color: #888889;
							cursor: pointer;
							img{
								width: 12px;
								height: 12px;
								margin-right: 4px;
							}
						}
						&:first-child{
							padding-left: 0;
						}
						&:last-child{
							padding-right: 0;
							&::after{
								background-color: transparent;
							}
						}
					}

				}

				//文案div样式
				.copywriting-div {
					width: 100%;
					//height: 100%;
					// min-height: 100px;
					//background-color: #dff;
					overflow-y: auto;
					outline: none;
				}

				/* 输入框 */
				.dzm-textarea {
					background: none;
					outline: none;
					//padding: 10px 10px 30px;
					//border: 1px solid #eeeeee;
					//border-radius: 4px;
					word-wrap: break-word;
					word-break: break-all;
					-webkit-user-modify: read-write-plaintext-only;
					&::selection {
						background-color: #BAD3F8;
						color: #202328; /* 选中时文字颜色 */
					}
					::v-deep(b){
						// background-color: red;
						&.match{
							background-color: #C8FF94;
							&.current{
								background-color: #94FFF1;
							}
						}
					}
				}

				/* 输入框为空时显示 placeholder */
				.dzm-textarea:empty:before {
					content: attr(placeholder);
					color: #cdcdcd;
				}

				/* 输入框获取焦点时移除 placeholder */
				.dzm-textarea:focus:before {
					// content: none;
					line-height: 18px;
				}

				//ai 文案div
				.AICopywriting_div {

					//background-color: #67c23a;
					&_top {
						width: 100%;
						border-radius: 20px;
						border: 2px solid #0AAF60;

						div {
							border: 1px solid #0AAF60;
						}

						::v-deep(.el-input__wrapper) {
							background-color: #F7F7F7 !important;
							border-radius: 40px !important;
							box-shadow: none;
						}
					}

					&_bottom {

						//background-color: #006eff;
						&_left {

							//background-color: #67c23a;
							&_item {
								background: #FFFFFF;
								border-radius: 16px;
								font-weight: 500;
								font-size: 12px;
								color: #0AAF60;
							}
						}

						&_right {
							font-weight: 400;
							color: #7E838D;
						}
					}
				}
				.speaker_content_bottom_left {
					position: absolute;
					bottom: 15px;
					left: 12px;
					flex-shrink: 0;
					&_left_button {
						background: #F5F5F5;
						position: relative;
						border: 1px solid #D3D3D2;
						border-radius: 2px;
						span {
							font-size: 14px;
							&:first-child {
								color: rgba(0, 0, 0, 0.45);
							}

							&:last-child {
								color: #353D49;
							}
						}
					}
				}
				// .ai_choose_voice{
				// 	position: fixed;
				// 	z-index: 3;
				// 	left: 36px;
				// 	// top: 50%;
				// 	// transform: translateY(-50%);
				// 	width: 100px;
				// 	height: 34px;
				// }
				// AI悬浮工具栏
				.ai_suspended_toolbar {
					position: fixed;
					z-index: 1;
					left: 66px;
					height: 36px;
					display: flex;
					align-items: center;
					padding: 6px 12px;
					//background: #F4FAFF;
					/* 纯色背景 */
					// box-shadow: 0px 4px 11px rgba(0, 0, 0, 0.1);
					border-radius: 8px;
					overflow: visible;
					margin-top: 10px;
					img{
						width:94px;
						height: 34px;
						cursor: pointer;
					}
					// .ai_suspended_toolbar_item {
					// 	position: relative;
					// 	padding: 0 12px;
					// 	cursor: pointer;
					// 	display: flex;
					// 	align-items: center;
					// 	justify-content: center;

					// 	img {
					// 		width: 12px;
					// 		height: 12px;
					// 		margin-right: 7px;
					// 	}

					// 	span {
					// 		font-size: 14px;
					// 		line-height: 24px;
					// 		color: #16162E;
					// 	}

					// 	&::after {
					// 		position: absolute;
					// 		top: 50%;
					// 		right: 0;
					// 		content: "";
					// 		width: 1px;
					// 		height: 11px;
					// 		background: #D3D3D2;
					// 		transform: translateY(-50%);
					// 	}

					// 	&:first-child {
					// 		padding-left: 0;
					// 	}

					// 	&:last-child {
					// 		padding-right: 0;

					// 		&::after {
					// 			display: none;
					// 		}

					// 	}
					// }

					// &:before {
					// 	content: "";
					// 	position: absolute;
					// 	top: 0;
					// 	left: 0;
					// 	right: 0;
					// 	bottom: 0;
					// 	border-radius: 8px;
					// 	padding: 1px;
					// 	/* 边框宽度 */
					// 	background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%);
					// 	/* 挖空中间区域，露出主元素背景 */
					// 	-webkit-mask:
					// 		linear-gradient(#fff 0 0) content-box,
					// 		linear-gradient(#fff 0 0);
					// 	-webkit-mask-composite: destination-out;
					// 	mask-composite: exclude;

					// 	pointer-events: none;
					// 	z-index: -1;
					// }

				}

				// AI文案工具栏悬浮
				.ai_copywriting_toolbar {
					position: fixed;
					z-index: 3;
					left: 66px;
					display: flex;
					flex-direction: column;
					width: 760px;
					margin-top: 10px;
					.ai_copywriting_toolbar_top {
						display: flex;
						align-items: center;
						padding-right: 10px;
						margin-bottom: 10px;

						.ai_copywriting_toolbar_top_list {
							display: flex;
							align-items: center;

							.ai_copywriting_toolbar_top_list_item {
								margin-right: 5px;
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								align-items: center;
								padding: 3px 10px;
								height: 30px;
								background: #FFFFFF;
								border: 1px solid #E2E2E2;
								border-radius: 8px;
								font-size: 14px;
								line-height: 24px;
								color: #16162E;
								cursor: pointer;

								&:last-child {
									margin-right: 0;
								}

								&.active {
									overflow: visible;
    								position: relative;
									// background: rgba(138, 112, 240, 0.05);
									// box-shadow: 0 2px 10px rgba(138, 112, 240, 0.15);
									&::after{
										content: '';
										position: absolute;
										top: -1px;
										left: 0;
										width: 100%;
										height: 100%;
										border-radius: 8px;
										padding: 1px;
										background: linear-gradient(290.19deg, #7956ff 28.18%, #3e9efd 78.95%);
										-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
										-webkit-mask-composite: destination-out;
										mask-composite: exclude;
										pointer-events: none;
										z-index: 1;
									}
								}
							}
						}

						.ai_copywriting_toolbar_top_more {
							margin-left: auto;
							display: flex;
							align-items: center;
							cursor: pointer;

							span {
								font-size: 14px;
								line-height: 24px;
								color: #16162E;
								margin-right: 4px;
							}

							img {
								width: 16px;
								height: 16px;
							}
						}
					}

					.ai_copywriting_container {
						z-index: 3;
						width: 100%;
						min-height: 36px;
						display: flex;
						padding: 13px;
						padding-left: 20px;
						background: #fff;
						/* 纯色背景 */
						border-radius: 8px;
						overflow: visible;
						position: relative;

						.ai_copywriting_textarea_img {
							width: 22px;
							height: 22px;
							margin-right: 16px;
							align-self: flex-start;
						}

						.ai_copywriting_toolbar_input {
							flex: 1;
							position: relative;
							z-index: 2;
							font-size: 14px;
							font-family: inherit;
							line-height: 20px;
							box-sizing: border-box;
							/* border: 1px solid #dcdfe6; */
							// border-radius: 20px;
							background: #fff;
							outline: none;
							padding: 0;
							color: #353D49;
							caret-color: #000;
							resize: none;
							overflow-y: auto;

							// 兼容placeholder
							&:empty:before {
								content: attr(placeholder);
								color: #bbb;
							}

						}

						.ai_copywriting_toolbar_buttons {
							align-self: flex-end;

							.ai_copywriting_toolbar_group {
								display: flex;
								align-items: center;
								gap: 15px;

								.ai_copywriting_toolbar_btn {
									padding: 6px;
									width: 30px;
									height: 30px;
									border: none;

									:hover {
										background-color: #fff;
									}

									img {
										width: 30px;
										height: 30px;
									}
								}
							}
						}

						&:before {
							content: "";
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
							height: 100%;
							border-radius: 8px;
							padding: 1px;
							/* 边框宽度 */
							background: linear-gradient(290.19deg, #7956FF 28.18%, #3E9EFD 78.95%);
							/* 挖空中间区域，露出主元素背景 */
							-webkit-mask:
								linear-gradient(#fff 0 0) content-box,
								linear-gradient(#fff 0 0);
							-webkit-mask-composite: destination-out;
							mask-composite: exclude;

							pointer-events: none;
							z-index: -1;
						}
					}
				}
			}


			//  中间主体右边
			.speaker_content_bottom {
				//background-color: #f00;

				.speaker_content_bottom_left {
					flex-shrink: 0;
					//margin-right:40px;
					//background-color: #ddd;
					//background-color: #67c23a;
					//::v-deep(.gradient-slider-popover) {
					//
					//  .speaker_content_bottom_left_speed_speech{
					//    .gradient-slider {
					//      /* 必须覆盖的基准色 */
					//      --el-slider-main-bg-color: transparent;
					//      .el-slider__bar {
					//        background: linear-gradient(90deg, #ff6b6b, #ff8787);
					//        height: 4px;
					//      }
					//      .el-slider__button {
					//        border-color: #ff6b6b;
					//        background: linear-gradient(45deg, #ff6b6b, #ff8787);
					//      }
					//    }
					//  }
					//
					//
					//}


					//
					//:deep(.custom-popover){
					//  .speaker_content_bottom_left_speed_speech{
					//    .gradient-slider{
					//
					//    }
					//          ::v-deep(.el-slider__bar){
					//            //background: #0aaf60;
					//            background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
					//          }
					//          ::v-deep(.el-slider__button){
					//            border: 2px solid #0aaf60;
					//          }
					//  }
					//}


					//&_speed_speech{
					//  //font-weight: 400;
					//  //font-size: 14px;
					//  //color: #121212;
					//  background-color: #f00;
					//
					//  ::v-deep(.el-popover){
					//
					//
					//    .slider{
					//       控制滑块的默认颜色
					//      :：v-deep(.el-slider__bar){
					//        //background: #0aaf60;
					//        background: linear-gradient( 108deg, #FFD600 0%, #0AAF60 100%);
					//      }
					//      ::v-deep(.el-slider__button){
					//        border: 2px solid #0aaf60;
					//      }
					//    }
					//
					//
					//  }
					//
					//}


					&_left_button {
						background: #F5F5F5;
						border-radius: 4px;

						span {
							&:first-child {
								color: #9d9d9d;
							}

							&:last-child {
								color: #565d66;
							}
						}
					}

					//.position_volume{
					//  position: absolute;
					//  top:20px;
					//  right: 20px;
					//  background-color: #fff;
					//  border-radius: 6px;
					//  &_span1{
					//
					//  }
					//  // 控制滑块的默认颜色
					//  ::v-deep(.el-slider__bar){
					//    background: #0aaf60;
					//  }
					//  ::v-deep(.el-slider__button){
					//    border: 2px solid #0aaf60;
					//  }
					//
					//  &_span2{}
					//}
				}

				//合成音频和快速试听按钮
				.speaker_content_bottom_middle {

					&_item_left,
					&_item_right {
						//width: 100px;
						//height: 40px;
						.span1,
						.span2 {
							font-weight: 500;
							font-size: 12px;
						}

						.span1 {
							color: #fff;
						}

						.span2 {
							color: #0BAF60;
						}
					}

					&_item_left {
						background: #0BAF60;
					}

					&_item_right {
						border: 1px solid #D3D3D2;
						font-size: 14px;
						line-height: 24px;
						border-radius: 4px;
						box-sizing: border-box;
						display: flex;
						justify-content: center;
						align-items: center;
						width: 90px;
						height: 40px;
						color: #353D49;
						.try_listen{
							width: 100%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							span{
								color: rgba(0, 0, 0, 0.45);
							}
							.try_listen_item{
								display: flex;
								align-items: center;
								justify-content: center;
								position: relative;
								flex: 1;
								height: 100%;
								img{
									width: 20px;
									height: 20px;
								}
								&::after{
									content: '';
									position: absolute;
									right: 0;
									top: 50%;
									transform: translateY(-50%);
									width: 1px;
									height: 10px;
									background-color: #EFEFF1;
									z-index: 1;
								}
								&:last-child{
									&::after{
										background-color: transparent;
									}
								}
							}
						}
						::v-deep(.el-loading-mask){
							z-index: 1;
						}
					}
				}

				//中间主体右边播放语音组件
				//.speaker_content_bottom_bottom{
				//
				//}
			}












		}



		//右边音色列表
		&_item_right {
			background-color: #fff;
			max-width: 688px;

			flex-shrink: 0;
			flex:1;
			display:flex;
			flex-direction:column;
			/* 禁止伸缩 */
			border-radius: 8px;
			position: relative;
			padding: 12px 24px;
			//overflow: hidden;
			&_top_search {
				padding-top: 7px;
				border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
				&_left {
					font-size: 14px;
					color: #000000;
					display: flex;
					align-items: center;
					.expand_list{
						width: 20px;
						height: 20px;
						margin-right: 1px;
					}
					.main_content_bottom_item_right_top_search_left_item{
						font-size: 14px;
						line-height: 51px;
						height: 51px;
						display: flex;
						align-items: center;
						color: rgba(0, 0, 0, 0.45);
						&.current{
							color: #353D49;
							border-bottom: 2px solid #FB6D30;
						}
						&:last-child{
							margin-right: 0;
						}
					}
					&::before {
						background-image: none;
					}
				}

				&_right {
					width: 250px;
					height: 37px;
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					background: #F6F7F9;
					padding: 4px 11px;
					.el-input {
						height: 30px;
						::v-deep(.el-input__wrapper) {
							padding: 0;
							//align-items: center;
							background-color: #F7F7F7 !important;
							border-radius: 40px !important;
							// border: none;
							// outline: none !important;
							box-shadow: none;
							.el-input__inner{
								font-size: 13px;
								display: flex;
								align-items: center;

								&::placeholder{
									color: #9DA3AC;
								}
							}
						}
					}
					img{
						margin-right: 4px;
						width: 16px;
						height: 16px;
					}

				}
			}

			//  选择项样式
			.speaker_content {

				//background-color: #006eff;
				//height:40px;
				&_list {
					flex-wrap: wrap;

					//background-color: #F7F7F9;
					//height:40px;
					&_item {
						font-size: 14px;
						line-height: 22px;
						color: #353D49;
						padding: 0
					}

					.is_active {
						//background: rgba(239, 179, 153, 0.46);
						border-radius: 4px;
						color: #FB6D30;
					}
				}
			}

			//  中间任务列表
			.speaker_content_middle {
				overflow-y: hidden;
				//background-color: #ddffff;
				//overflow-y: auto;
				&_list {
					flex-wrap: wrap;

					&_item {
						background-color: #F7F7F9;
						border-radius: 8px;
						position: relative;
						overflow: hidden;

						//珍享和精品图片div
						.position_image {
							position: absolute;
							top: 0;
							left: 0;
							z-index: 1;
						}

						//收藏图片图片
						.collectIcon {
							position: absolute;
							top: 6px;
							right: 14px;
						}

						&_insideDiv {
							border-radius: 8px;
							background-color: #F7F7F9;
							position: relative;
							&_avatar {
								position: relative;

								//background-color: #006eff;
								&_position_div {
									display: none;
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&_position_zanting {
									position: absolute;
									border-radius: 20px;
									right: 0;
									bottom: 0;
									background: rgba(18, 18, 18, 0.6);
								}

								&:hover {

									//background-color: #006eff;
									.speaker_content_middle_list_item_insideDiv_avatar_position_div {
										display: flex;
										position: absolute;
										border-radius: 20px;
										right: 0;
										bottom: 0;
										line-height: 0;
										background: rgba(18, 18, 18, 0.6);
									}
								}
							}

							&_nickName {
								font-size: 14px;
								line-height: 18px;
								color: #1B2337;
								margin-top: 5px;
							}

							.matching_degree {
								//background: #fff;
								border-radius: 6px;
								padding: 3px 10px;
								color: #0AAF60;
								font-size: 12px;
							}

						}
						.speaker_content_middle_list_item_collect{
							position: absolute;
							top: 9px;
							right: 9px;
							width: 16px;
							height: 14px;
							z-index: 1;
						}
					}
				}
			}
			.speaker_content_middle{
				flex:1;
				display:flex;
				.el-scrollbar{
					flex:1;
					display:flex;
					::v-deep(.el-scrollbar__wrap--hidden-default){
						flex:1;
						display:flex;
						.el-scrollbar__view{
							flex:1;
							display:flex;
							// justify-content: center;
							.el-empty{
								margin: 0 auto;
							}
							.speaker_content_middle_list{
								height: fit-content;

							}
						}
					}
				}
			}
			.fold_list_btn{
				position: absolute;
				top: 50%;
				left: -18px;
				transform: translateY(-50%);
				z-index: 1;
				width: 24px;
				height: 62px;
			}
		}
	}

	//步骤条等于1时，隐藏步骤条
	.main_content_bottom1 {
		// height: calc(100vh - 160px);
		flex:1;
		min-width: 1100px;
		box-sizing: border-box;
		//background-color: #67c23a;
		//flex-shrink: 0;
		&_content {
			width: 80%;
			margin: 0 auto;

			//flex-shrink: 0;
			&_item {
				width: 100%;
				height: 165px;
				background-color: #fff;
				border-radius: 12px;

				&_left {
					position: relative;
					flex-shrink: 0;

					.position_img {
						position: absolute;
						top: 0;
						left: 0;
					}

					span {
						font-weight: 400;
						font-size: 14px;
						color: #121212;
					}
				}

				&_right {
					&_top {
						letter-spacing: 2px;
						line-height: 22px;
						flex-shrink: 0;
						//background-color: #ff4d4f;
						display: -webkit-box;
						-webkit-line-clamp: 3;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
						font-weight: 400;
						font-size: 14px;
						color: #121212;
					}

					&_bottom {

						//background-color: #5b5b5b;
						&_left {
							&_palyDiv {
								span {
									font-weight: 400;
									font-size: 14px;
									color: #7E838D;
								}
							}
						}

						&_right {}
					}
				}
			}
		}
	}


	//步骤条等于2时，隐藏步骤条
	.main_content_bottom2 {
		flex:1;
		// height: calc(100vh - 160px);
		min-width: 1100px;

		//background-color: #67c23a;
		//flex-shrink: 0;
		&_content {
			width: 80%;
			margin: 0 auto;

			//flex-shrink: 0;
			&_item {
				width: 100%;
				//min-height:150px;
				background-color: #fff;
				border-radius: 12px;

				&_left {
					position: relative;
					flex-shrink: 0;
					min-height: 150px;

					.position_img {
						position: absolute;
						top: 0;
						left: 0;
					}

					span {
						font-weight: 400;
						font-size: 14px;
						color: #121212;
					}
				}

				&_right {
					min-height: 150px;

					&_top {
						letter-spacing: 2px;
						line-height: 22px;
						flex-shrink: 0;
						//background-color: #ff4d4f;
						//display: -webkit-box;
						//-webkit-line-clamp: 3;
						//-webkit-box-orient: vertical;
						//overflow: hidden;
						//text-overflow: ellipsis;
						font-weight: 400;
						font-size: 14px;
						color: #121212;

						&_content {
							background-color: #f7f7f9;
							border-radius: 8px;
							padding: 10px;
						}
					}

					&_bottom {

						//background-color: #5b5b5b;
						&_left {
							&_palyDiv {
								span {
									font-weight: 400;
									font-size: 14px;
									color: #7E838D;
								}
							}
						}

						&_right {}
					}
				}
			}
		}
	}


	//  进度条等于3的时候的样式
	.main_content_sider_div {
		flex:1;
		// height: calc(100vh - 160px);

		//中间左边大div样式
		&_left {
			background-color: #fff;
			flex: 1;
			min-width: 650px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;
			//&_top{
			//  height:70px;
			//  //background-color: #888888;
			//  &_right{
			//    background-image: url("@/assets/images/aiImages/top_right_bg.png");
			//    background-repeat: no-repeat;
			//    background-size: cover;
			//    position: relative;
			//    .position-icon1{
			//      position: absolute;
			//      top: -7px;
			//      right:-7px;
			//    }
			//    &_left{
			//      &_right{
			//        div{
			//          overflow: hidden;
			//          text-overflow: ellipsis;
			//          white-space: nowrap;
			//          &:first-child{
			//            color:#121212;
			//          }
			//          &:last-child{
			//            color: #7E838D;
			//          }
			//        }
			//      }
			//    }
			//    &_right{
			//
			//    }
			//  }
			//}

			//左边列表播放div列表
			&_bottom {
				flex: 1;
				background-color: #fff;
				border-radius: 8px;
				position: relative;
				//outline: none;
				//overflow-x:hidden;
				overflow-y: auto;
				position: relative;

				//列表样式
				&_item {
					border-radius: 8px;

					&_left {
						width: 200px;
						.el-button.is-circle{
							width: 20px;
							height: 20px;
						}
						//background-color: #ff4d4f;
						span {
							font-weight: 400;
							font-size: 14px;
							color: #121212;
							display: inline-block;
							white-space: nowrap;
							/* 禁止换行 */
							overflow: hidden;
							/* 隐藏溢出内容 */
							text-overflow: ellipsis;
							/* 超出用省略号表示 */
							/* 设置容器宽度 */
							//background-color: #ff4d4f;

						}
					}
				}





			}


		}

		//中间右边大div样式
		&_right {
			background-color: #f7f7f9;
			max-width: 600px;
			flex-shrink: 0;
			/* 禁止伸缩 */
			border-radius: 8px;

			&_top {
				background-color: #fff;
				//height: 50%;
				//overflow-y: auto;




				&_top {
					height: 30px;

					//background-color: #888888;
					&_right {
						background-image: url("@/assets/images/aiImages/top_right_bg.png");
						background-repeat: no-repeat;
						background-size: cover;
						position: relative;

						.position-icon1 {
							position: absolute;
							top: -7px;
							right: -7px;
						}

						&_left {
							&_right {
								div {
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;

									&:first-child {
										color: #121212;
									}

									&:last-child {
										color: #7E838D;
									}
								}
							}
						}

						&_right {}
					}
				}











				&_div1 {
					span {
						font-weight: 500;
						color: #121212;
					}
				}

				&_div2 {
					background: #F7F7F9;
					border-radius: 8px;
					border: 1px solid rgba(126, 131, 141, 0.2);
					margin: 2% 0;
					line-height: 22px;
					letter-spacing: 2px;
					font-weight: 500;
					outline: none;
					overflow-y: auto;
				}

				&_div3 {
					margin: 0;
					margin-bottom: 12px;
					padding:8px 16px;
					&_span_first {
						font-weight: 400;
						font-size: 14px;
						color: #353D49;
					}
					.el-slider{
						height: fit-content;
						::v-deep(.el-slider__runway){
								height: 4px;
								background-color: #dcdcdc;
								margin-right: 36px;
							.el-slider__bar{
								background-color: #0AAF60;
								height: 4px;
							}
							.el-slider__button{
								background-color: #0AAF60;
								border: none;
								width: 12px;
								height: 12px;
								margin-top: -2px
							}
						}
						::v-deep(.el-input-number){
							height: 22px;
							border: 1px solid #E8E8E8;
							border-radius: 4px;
							box-sizing: border-box;
							.el-input-number__decrease{
								left:7px;
								background-image: url('@/assets/images/aiImages/slider_audio_reduce.svg');
								background-size: 8px 1px;
							}
							.el-input-number__increase{
								right: 7px;
								background-image: url('@/assets/images/aiImages/slider_audio_add.svg');
								background-size: 8px 8px;
							}
							.el-input-number__decrease,.el-input-number__increase{
								width: 8px;
								height: 8px;
								display: flex;
								align-items: center;
								justify-content: center;
								background-color: transparent;
								color: #000;
								top: 50%;
								transform: translateY(-50%);
								border: none;
								font-size: 0;
								background-position: center center;
								background-repeat: no-repeat;
							}
							.el-input{
								.el-input__wrapper{
									background-color: transparent;
									padding: 0 18px;
									border: none;
									box-shadow: none;
									position: relative;

									.el-input__inner{
										color: #000;
										padding-right: 13px; /* 给单位留空间 */
										font-size: 12px;
									}
									&::after {
										content: attr(data-unit);
										position: absolute;
										right: 0;
										top: 0;
										font-size: 12px;
										width: 30px;
										height: 30px;
										z-index: 1000;
										line-height: 20px;
										// transform: translateY(-50%);
										color: #000;
									}
								}
							}
						}
						&.intonation_slider{
							::v-deep(.el-input-number){
								width: 80px;
							}
						}
						&.speech_slider{
							::v-deep(.el-slider__input){
								width: 80px;
							}
						}
				}
				&:last-child{
					margin-bottom: 0;
				}
				}

				&_div4 {}

			}

			&_bottom {
				background-color: #fff;

				//height: 50%;
				&_div1 {

					&_left {
						span {
							font-weight: 500;
							color: #121212;
						}
					}

					&_right {
						position: relative;
						background: linear-gradient(90deg, #0AAF60 0%, #A4CB55 99%);
						border-radius: 20px;

						.position-icon1 {
							position: absolute;
							top: -7px;
							right: -3px;
						}

						&_left {
							position: relative;

							.position_volume {
								position: absolute;
								top: -180px;
								left: -15px;
								background: rgba(29, 33, 41, 0.85);
								border-radius: 6px;

								&_span1,
								&_span2 {
									color: #fff;
								}

								// 控制滑块的默认颜色
								::v-deep(.el-slider__bar) {
									background: #0aaf60;
								}

								::v-deep(.el-slider__button) {
									border: 2px solid #0aaf60;
								}


							}

							span {
								font-weight: 400;
								font-size: 12px;
								color: rgba(255, 255, 255, 0.85);
								white-space: nowrap;
								/* 禁止换行 */
								overflow: hidden;
								/* 隐藏溢出内容 */
								text-overflow: ellipsis;
								/* 超出用省略号表示 */
								width: 60px;
								//background-color: #ff4d4f/* 设置容器宽度 */
							}
						}

						&_right {

							font-weight: 400;
							font-size: 12px;
							color: #FFFFFF;
						}
					}







				}

				&_div2 {
					//background-color: #ff4d4f;
					display: flex;
					align-items: center;
					margin: 4% 0;
					//border-radius: 20px;
				}

				&_div3 {
					//background-color: #ff4d4f;
					//margin-top: 30%;

				}

			}

		}
	}




}

// 搜索高亮样式
:deep(span[style*="color: #0AAF60"]) {
	color: #0AAF60 !important;
	font-weight: bold !important;
}

.color-gray {
	color: #7E838D;
}

.speaker_content_middle_list_item_insideDiv {
	&_tags {
		max-width: 90px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
</style>
<style lang="scss">
.gradient-slider-popover{
	&.is-light {
		padding: 8px 9px 8px 16px;
		background: rgba(29, 33, 41, 0.85);
		border-radius: 2px;

		.el-slider__runway {
			background: rgba(255, 255, 255, 0.65);
			height: 2px;

			.el-slider__bar{
				height: 2px;
				background-color: #fff;
			}
		}

		.el-slider__button-wrapper {
			top: 50%;
			height: 100%;
		}

		.el-slider__stop {
			background: #EFF0F3;
			box-sizing: border-box;
			width: 4px;
			height: 4px;
			top: 50%;
			transform: translateY(-50%);
		}

		.el-slider__button {
			width: 12px;
			height: 12px;
			box-sizing: border-box;
			top: 50%;
			border: none;
		}



		.el-slider__marks {
			display: none;
		}
		.el-slider__input {
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 0px;
			height: 22px;
			border: 1px solid #4A4A4A;
			border-radius: 4px;
			flex: none;
			.el-input-number__decrease{
				left:7px;
				background-image: url('@/assets/images/aiImages/slider_reduce.svg');
				background-size: 8px 1px;
			}
			.el-input-number__increase{
				right: 7px;
				background-image: url('@/assets/images/aiImages/slider_add.svg');
				background-size: 8px 8px;
			}
			.el-input-number__decrease,.el-input-number__increase{
				width: 8px;
				height: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: transparent;
				color: #Fff;
				top: 50%;
				transform: translateY(-50%);
				border: none;
				font-size: 0;
				background-position: center center;
				background-repeat: no-repeat;
			}
			.el-input{
				.el-input__wrapper{
					background-color: transparent;
					padding: 0 18px;
					border: none;
					box-shadow: none;
					position: relative;
					.el-input__inner{
						color: #fff;
						padding-right: 13px; /* 给单位留空间 */
					}
					&::after {
						content: attr(data-unit);
						position: absolute;
						right: 0;
						top: 0;
						width: 30px;
						height: 30px;
						z-index: 1000;
						// transform: translateY(-50%);
						color: #fff;
						// pointer-events: none;
						// user-select: none;
						font-size: 14px;
					}
				}
			}
		}
		.el-popper__arrow {
			&::before {
				background-color: transparent;
				border: none;
				background-image: url('@/assets/images/aiImages/gradient_arrow.png');
				background-position: 0 0;
				background-repeat: no-repeat;
				background-size: 11px 5px;
				transform: rotate(360deg);
				top: 5px;
			}
		}
		&.intonation_popover{
			.el-slider__input{
				width: 80px;
			}
		}
		&.speech_popover{
			.el-slider__input{
				width: 80px;
			}
		}
		&.volume_popover{
			.el-slider__input{
				width: 78px;
			}
		}
	}
}
.right_listen{
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	width: 72px;
	height: 30px;
	background: #353D49;
	border: 1px solid #29313D;
	box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.13);
	border-radius: 5px;
	font-size: 14px;
	color:#fff;
	img{
		width: 13px;
		height: 13px;
		right: 3px;
	}
	&:hover{
		background: #353D49;
		color:#fff;
	}

}
.full_screen_loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 半透明遮罩 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
  .full_screen_loading_bar_boxs {
	display: flex;
	align-items: center;
	gap: 7px;
	height:41px;
	margin-bottom: 58px;
    .full_screen_loading_bar {
		width: 6px;
		height: 25px;
		background-color: #80F1AD; /* 默认浅绿色 */
		border-radius: 3px;
		animation-name: waveColor;
		animation-duration: 1.8s;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
		transform-origin: center center;
		&:nth-child(1) {
			animation-delay: 0s;
		}
		&:nth-child(2) {
			animation-delay: 0.3s;
		}
		&:nth-child(3) {
			animation-delay: 0.6s;
		}
		&:nth-child(4) {
			animation-delay: 0.9s;
		}
		&:nth-child(5) {
			animation-delay: 1.2s;
		}
		&:nth-child(6) {
			animation-delay: 1.5s;
		}
		// &.active {
		// 	background-color: #0AAF60; /* 深绿色 */
		// 	opacity: 1;
		// }
	}
 }
	span{
		font-weight: 500;
		font-size: 15px;
		line-height: 21px;
		letter-spacing: -0.02em;
		color: #FFFFFF;
	}

}
/* 动画关键帧 */
@keyframes waveColor {
  0%, 100% {
    transform: scaleY(1);
    background-color: #80F1AD; /* 浅绿色 */
    opacity: 1;
  }
  50% {
    transform: scaleY(1.8);
    background-color: #0AAF60; /* 深绿色 */
    opacity: 1;
  }
}
.el-popper{
	&.is-light{
		&.action_bar_popover{
			padding: 0;
			border-radius: 3px;
			box-shadow: 0px 4px 18px rgba(0, 0, 0, 0.04);
			.el-popper__arrow{
				display: none;
			}
		}
	}
}
.try_listen-tooltip{
	// margin-left: 24px;
	margin-top: -20px;
}
.aibubbing_download_menu{
	height: auto;
	z-index: 9999 !important;
}
</style>

