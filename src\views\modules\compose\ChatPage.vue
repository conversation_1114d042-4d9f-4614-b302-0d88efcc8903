<template>
    <div class="chat-container">
        <div class="message-list" ref="messageList">
            <div v-for="(msg, index) in messages" :key="index" class="message-item" :class="{
                'user-message': msg.isUser,
                'ai-message': !msg.isUser,
            }" @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = -1">
                <!-- 刷新加载状态 -->
                <div v-if="msg.isRefreshing" class="bubble thinking-bubble">
                    <div class="thinking-text">
                        <span>💭</span>
                        <div class="thinking-wave">重新生成中...</div>
                    </div>
                    <span class="timestamp">🕒 {{ msg.refreshTime }}</span>
                </div>
                <div class="bubble" v-else>
                    <div class="message-content">
                        <span v-if="msg.isUser" class="user-text">{{
                            msg.content
                        }}</span>
                        <div v-else class="ai-text markdown-body"
                            v-html="renderMarkdown(msg.isTyping ? msg.displayContent : msg.content)"></div>
                    </div>
                    <!-- 只在AI消息显示操作按钮 -->
                    <div v-if="!msg.isUser" class="action-buttons" :class="{
                        'always-visible':
                            msg.isNew ||
                            hoverIndex === index ||
                            isLatestAIMessage(index),
                    }">
                        <div class="action-button-wrapper">
                            <el-button link @click="copyText(msg.content)" aria-label="复制">
                                <el-icon>
                                    <DocumentCopy />
                                </el-icon>
                                <span class="button-text">复制</span>
                            </el-button>
                        </div>
                        <div class="action-button-wrapper">
                            <el-button link @click="refreshText(index, msg)" aria-label="刷新">
                                <el-icon>
                                    <Refresh />
                                </el-icon>
                                <span class="button-text">刷新</span>
                            </el-button>
                        </div>
                        <div class="action-button-wrapper">
                            <el-button link @click="saveText(index)" aria-label="收藏">
                                <el-icon>
                                    <Star />
                                </el-icon>
                                <span class="button-text">收藏</span>
                            </el-button>
                        </div>
                        <div class="action-button-wrapper">
                            <el-button link @click="deleteText(index)" aria-label="删除">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                                <span class="button-text">删除</span>
                            </el-button>
                        </div>
                    </div>
                    <span class="timestamp">🕒 {{ msg.time }}</span>
                </div>
            </div>

            <div v-if="isThinking" class="message-item">
                <div class="bubble thinking-bubble">
                    <div class="thinking-text">
                        <span>💭</span>
                        <div class="thinking-wave">正在思考中</div>
                    </div>
                    <span class="timestamp">🕒 {{ getCurrentTime() }}</span>
                </div>
            </div>
        </div>

        <div class="input-footer">
            <div class="custom-input-container">
                <el-input v-model="inputText" @keydown.enter="handleKeydown" type="textarea" :rows="3"
                    placeholder="请输入你要撰写的主题" resize="none" class="custom-input" />

                <div class="action-buttons">
                    <div class="button-group">
                        <el-tooltip content="历史记录" placement="top" effect="light" :popper-style="{
                            border: 'none',
                            padding: '8px 12px',
                        }" :offset="1" :show-arrow="false">
                            <el-button @click="goToHistory" link class="icon-btn">
                                <img class="icon-img" src="../../../assets/img/26.png" />
                            </el-button>
                        </el-tooltip>

                        <span class="divider"></span>

                        <el-tooltip content="返回到主页" placement="top" effect="light" :popper-style="{
                            border: 'none',
                            padding: '8px 12px',
                        }" :offset="1" :show-arrow="false">
                            <el-button @click="emit('back')" link class="icon-btn">
                                <img class="icon-img" src="../../../assets/img/19.png" />
                            </el-button>
                        </el-tooltip>

                        <span class="divider"></span>

                        <el-button @click="sendMessage" link class="icon-btn" circle>
                            <img class="icon-img" src="../../../assets/img/20.png" />
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存到我的空间 -->
        <SubtitleDialog v-model="showSaveDialog" :material-info="materialInfo" :sourcePage="'ai-tool'" @confirm="saveToFavorites" />

        <!-- 添加会员限制弹窗 -->
        <AlertDialog v-model:visible="showLimitDialog" type="warning" title="会员功能" message="非会员每日只能使用15次，请开通会员使用"
            confirm-button-text="开通会员" cancel-button-text="我知道了" :show-cancel-button="true" :custom-confirm-class="true"
            :custom-cancel-class="true" :show-fee-explanation="false" @confirm="handleOpenMember"
            @cancel="handleCloseLimitDialog" />
    </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onUnmounted, getCurrentInstance } from "vue";
import SubtitleDialog from "../../../components/SubtitleDialog/index.vue";
import { ElMessage } from "element-plus";
import { useMessageStore } from "@/stores/message";
import { storeToRefs } from "pinia";
import { questionAPl, getChatRecordApi } from "@/api/creation";
import { usePageStore } from "@/stores/page";
import {
    DocumentCopy,
    Refresh,
    Star,
    Delete,
    Promotion,
} from "@element-plus/icons-vue";
import AlertDialog from "@/views/components/AlertDialog.vue"; // 引入AlertDialog组件
import { useRouter } from 'vue-router'; // 引入路由
import { saveFullMaterial } from "@/api/myMaterial";

// 获取组件实例
const { proxy } = getCurrentInstance();
// 获取路由实例
const router = useRouter();

// 初始化store和获取消息，确保先于其他代码
const messageStore = useMessageStore();
const { messages } = storeToRefs(messageStore); // 从状态管理获取消息

// 新增加载状态
const isThinking = ref(false);
const props = defineProps({
    content: String, // 接收传递的内容
});

// 判断用户是否已登录
const checkUserLogin = () => {
    // 从本地存储获取user信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;

    try {
        const userData = JSON.parse(userStorage);
        // 检查token是否为null或空
        return userData && userData.token && userData.token.trim() !== '';
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

// 获取消息容器DOM引用
const messageList = ref(null);

// 添加获取userId的辅助函数
const getUserId = () => {
    try {
        return JSON.parse(localStorage.getItem('user'))?.userId || '';
    } catch (error) {
        console.error('获取userId失败:', error);
        return '';
    }
};

// 监听messages变化自动滚动
watch(
    () => messages.value.length,
    async () => {
        await nextTick(); // 等待DOM更新完成
        scrollToBottom();
    },
    { deep: true }
);
watch(
    () => messages.value,
    (value) => {
        if (value.length > 0) {
            value.map((item) => {
                isThinking.value = item.isThinking;
            });
        }
    },
    {
        deep: true,
        immediate: true,
    }
);

// 修改后的滚动方法
const scrollToBottom = () => {
    if (messageList.value) {
        messageList.value.scrollTo({
            top: messageList.value.scrollHeight,
            behavior: "smooth", // 添加平滑滚动效果
        });
        // isThinking.value = false
    }
};
// 组件挂载时自动滚动
onMounted(() => {
    scrollToBottom();
    // 同步全局状态
});
// 新增判断函数
const isLatestAIMessage = (index) => {
    return (
        index === messages.value.length - 1 &&
        !messages.value[index].isUser &&
        messages.value.some((msg) => msg.isUser) // 确保有用户消息时才显示
    );
};

const inputText = ref("");
const hoverIndex = ref(-1);
const showSaveDialog = ref(false); // 控制收藏对话框显示
const materialInfo = ref({}); // 新增：保存参数对象

// 添加打字效果定时器引用
const typingTimer = ref(null);

const questionChange = async (inputText) => {
    try {
        const res = await questionAPl({
            userId: getUserId(),
            question: inputText,
        });
        return res.content.result.ai;
    } catch (err) {
        console.error("获取失败:", err);
    }
};
const handleKeydown = (event) => {
    console.log(event, 111);

    // 检查是否同时按下Shift键
    if (!event.shiftKey) {
        event.preventDefault(); // 阻止默认换行行为
        sendMessage();
    }
    // 如果按住Shift+Enter则允许换行
};

const sendMessage = async () => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

    if (!inputText.value || !inputText.value.trim()) {
        ElMessage.error("请输入您要撰写的主题");
        return;
    }

    // 添加用户消息（右侧）
    const userMsg = {
        content: inputText.value,
        time: getCurrentTime(),
        isUser: true,
        isNew: true,
        isThinking: true,
    };
    // 添加思考状态
    isThinking.value = true;
    messages.value.push(userMsg);

    // 保存当前输入内容
    const lastSendText = inputText.value;

    // 清空输入框
    inputText.value = "";

    try {
        // 准备历史记录，添加上下文功能
        let questionWithHistory = lastSendText;

        // 如果有历史记录，将其格式化后添加到问题中
        if (messages.value && messages.value.length > 1) {
            // 收集之前的所有消息记录，格式化为 user：xxx 和 ai：xxx
            const historyText = messages.value.slice(0, -1).map(msg => {
                if (!msg) return '';
                return `${msg.isUser ? 'user：' : 'ai：'}${msg.content || ''}`;
            }).join('\n');

            // 将历史记录和当前问题组合
            questionWithHistory = `${historyText}\nuser：${lastSendText}`;
        }

        // 获取userId，确保调用API时不会因为null而出错
        const userId = getUserId() || '';
        
        // 获取AI回复（左侧），使用包含历史记录的问题
        const res = await questionAPl({
            userId: userId,
            question: questionWithHistory,
        });

        // 检查会员限制状态码（仅检查310）
        if (res.status_code === 310) {
            // 立即停止思考状态
            isThinking.value = false;

            // 添加一个会员限制提示消息
            const limitMsg = {
                content: "由于会员限制，无法继续生成内容。",
                time: getCurrentTime(),
                isUser: false,
                isNew: true,
                isThinking: false,
            };
            messages.value.push(limitMsg);

            // 显示会员限制弹窗
            showLimitDialog.value = true;

            return;
        }

        // 检查响应和结果是否存在
        if (!res || !res.content || !res.content.result || !res.content.result.ai) {
            throw new Error("API返回格式不符合预期");
        }

        const AIvalue = res.content.result.ai;

        // 移除思考状态
        isThinking.value = false;

        // 修改：添加AI响应，增加打字效果相关字段
        const botMsg = {
            content: AIvalue, // 存储完整内容，用于复制和其他操作
            displayContent: "", // 用于显示的逐步增加的内容
            time: getCurrentTime(),
            isUser: false,
            isNew: true,
            isThinking: false,
            isTyping: true, // 标记正在打字中
        };

        messages.value.push(botMsg);

        // 启动打字效果
        startTypingEffect(messages.value.length - 1, AIvalue);

        // 1秒后取消新消息状态
        setTimeout(() => {
            messages.value.forEach((msg) => (msg.isNew = false));
            userMsg.isNew = false;
            botMsg.isNew = false;
            scrollToBottom();
        }, 1000);
    } catch (error) {
        console.error("发送消息错误:", error);
        isThinking.value = false;
        inputText.value = lastSendText;

        // 检查是否是会员限制错误（仅检查310）
        if (error.response && error.response.data && error.response.data.status_code === 310) {
            // 添加会员限制提示消息
            const limitMsg = {
                content: "由于会员限制，无法继续生成内容。",
                time: getCurrentTime(),
                isUser: false,
                isNew: true,
                isThinking: false,
            };
            messages.value.push(limitMsg);

            // 显示会员限制弹窗
            showLimitDialog.value = true;
        } else {
            // 添加错误消息
            const errorMsg = {
                content: "生成失败，请稍后重试",
                time: getCurrentTime(),
                isUser: false,
                isNew: true,
                isThinking: false,
            };
            messages.value.push(errorMsg);

            ElMessage.error("生成失败，请稍后重试");
            console.error("生成失败:", error);
        }
    }
};

// 添加打字效果函数
const startTypingEffect = (messageIndex, fullText) => {
    // 确保不超出数组范围
    if (messageIndex < 0 || messageIndex >= messages.value.length) {
        return;
    }

    // 初始化显示内容为空
    messages.value[messageIndex].displayContent = "";

    // 字符计数器
    let charIndex = 0;
    // 字符总数
    const totalChars = fullText.length;

    // 清除可能存在的旧定时器
    if (typingTimer.value) {
        clearInterval(typingTimer.value);
    }

    // 设置定时器逐字显示
    typingTimer.value = setInterval(() => {
        // 每次显示一个新字符
        charIndex++;

        // 更新显示内容
        messages.value[messageIndex].displayContent = fullText.substring(0, charIndex);

        // 自动滚动到底部，保持最新消息可见
        scrollToBottom();

        // 所有字符显示完成后清除定时器
        if (charIndex >= totalChars) {
            clearInterval(typingTimer.value);
            messages.value[messageIndex].isTyping = false; // 打字效果结束
            typingTimer.value = null;
        }
    }, 80 + Math.random() * 40); // 调整为更慢的速度：80-120毫秒
};

// 辅助函数
const getCurrentTime = () =>
    new Date().toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
    });

const copyText = (text) => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

    // 尝试使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        // 安全上下文中使用 navigator.clipboard
        navigator.clipboard
            .writeText(text)
            .then(() => {
                ElMessage.success("复制成功");
            })
            .catch(() => {
                // 如果现代API失败，回退到传统方法
                fallbackCopyTextToClipboard(text);
            });
    } else {
        // 不支持现代API或非安全上下文，使用传统方法
        fallbackCopyTextToClipboard(text);
    }
};

// 传统复制方法
const fallbackCopyTextToClipboard = (text) => {
    try {
        // 创建一个临时文本区域
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 设置样式使其不可见
        textArea.style.position = "fixed";
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.width = "2em";
        textArea.style.height = "2em";
        textArea.style.padding = "0";
        textArea.style.border = "none";
        textArea.style.outline = "none";
        textArea.style.boxShadow = "none";
        textArea.style.background = "transparent";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 执行复制命令
        const successful = document.execCommand("copy");

        // 移除临时元素
        document.body.removeChild(textArea);

        if (successful) {
            ElMessage.success("复制成功");
        } else {
            ElMessage.error("复制失败");
        }
    } catch (err) {
        ElMessage.error("复制失败");
        console.error("复制失败:", err);
    }
};

const deleteText = (index) => {
    messages.value.splice(index, 1);
};

// 收藏弹窗方法
const modelValue = ref(false);
const defaultSelected = ref(["默认收藏夹"]);
// 接收选中结果
const handleSave = (selected) => {
    console.log("用户选择的文件夹:", selected);
    // 执行保存操作...
    ElMessage.success("收藏成功");
};

// 点击收藏按钮
const saveText = async (index) => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        proxy.$modal.open('组合式标题');
        return;
    }
    const msg = messages.value[index];
    materialInfo.value = {
        textContent: msg.content,
        materialName: msg.content.slice(0, 10),
        userId: getUserId(),
        materialType: 'text',
        duration: msg.content.length, // 字数
        fileSize: 1024 * 1024, // 1MB = 1024 * 1024 bytes
        // 其余可选字段可补充
    };
    showSaveDialog.value = true;
};

// 保存到收藏夹
const saveToFavorites = async (params) => {
    const required = ['textContent', 'materialName', 'userId', 'materialType', 'tagId'];
    for (const key of required) {
        if (!params[key]) {
            ElMessage.error(`${key}为必填项`);
            return;
        }
    }
    try {
        await saveFullMaterial(params);
        ElMessage.success("收藏成功");
    } catch (error) {
        ElMessage.error("收藏失败");
        console.error("收藏失败:", error);
    }
};

const refreshText = async (index, msg) => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

    // 保存原始内容用于错误恢复
    const originalContent = msg.content;
    const originalTime = msg.time;

    // 立即清空内容并进入加载状态
    messages.value[index] = {
        ...msg,
        content: "", // 清空内容
        isRefreshing: true, // 开启加载状态
        refreshTime: getCurrentTime(), // 记录刷新时间
        originalContent, // 保存原始内容
        originalTime, // 保存原始时间
    };

    try {
        // 调用API获取新内容
        const res = await questionAPl({
            userId: getUserId(),
            question: messages.value.find(m => m.isUser)?.content || "请创建内容"
        });

        // 获取新内容
        const newContent = res.content.result.ai;

        // 更新消息对象，准备打字效果
        messages.value[index] = {
            ...msg,
            content: newContent, // 存储完整内容
            displayContent: "", // 用于显示的内容初始为空
            isRefreshing: false,
            isTyping: true, // 标记为打字中
            time: getCurrentTime(), // 更新时间
        };

        // 启动打字效果
        startTypingEffect(index, newContent);
    } catch (error) {
        // 恢复原始内容
        messages.value[index] = {
            ...msg,
            content: originalContent,
            time: originalTime,
            isRefreshing: false,
        };
        ElMessage.error("刷新失败");
    }
};

const pageStore = usePageStore();

// 添加跳转到历史记录页面的方法
const goToHistory = () => {
    // 检查用户登录状态
    if (!checkUserLogin()) {
        // 用户未登录，弹出登录弹窗
        proxy.$modal.open('组合式标题');
        return;
    }

    // 直接使用 pageStore 的状态
    pageStore.currentView = "home";
    pageStore.showHistoryList = true;

    // 如果需要加载历史数据，可以在这里调用相关方法
    // 例如：getHistoryData(1);
};

const emit = defineEmits(["back"]);

import { marked } from 'marked';
import 'highlight.js/styles/github.css'; // 代码高亮样式
import hljs from 'highlight.js';

// 配置 marked
marked.setOptions({
    highlight: function (code, lang) {
        if (lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        }
        return hljs.highlightAuto(code).value;
    },
    breaks: true, // 支持 GitHub 风格的换行
    gfm: true, // 启用 GitHub 风格的 Markdown
});

// 添加 Markdown 渲染函数
const renderMarkdown = (content) => {
    try {
        // 先处理【】中的内容，将其替换为带有特定颜色的 span 标签
        const processedContent = content.replace(/【([^】]+)】/g, '<span style="color: #18AD25">【$1】</span>');
        return marked(processedContent);
    } catch (e) {
        console.error('Markdown 渲染错误:', e);
        return content;
    }
};

// 组件卸载时清理定时器
onUnmounted(() => {
    if (typingTimer.value) {
        clearInterval(typingTimer.value);
        typingTimer.value = null;
    }
});

// 添加会员限制相关状态
const showLimitDialog = ref(false);

// 会员限制处理方法
const handleOpenMember = () => {
    // 关闭弹窗
    showLimitDialog.value = false;

    // 导航到会员页面
    try {
        // 判断是否在layout布局内
        if (router && router.currentRoute && router.currentRoute.value.path.includes('/layout')) {
            // 如果在layout布局内，使用内部路由导航
            router.push({ name: 'membership-nav' });
        } else {
            // 否则通过URL跳转
            window.location.href = '/membership';
        }
        ElMessage.success("正在跳转到会员页面");
    } catch (error) {
        console.error("导航到会员页面失败:", error);
        ElMessage.error("导航到会员页面失败，请手动前往会员中心");
    }
};

const handleCloseLimitDialog = () => {
    // 关闭会员限制弹窗
    showLimitDialog.value = false;
};
</script>

<style lang="scss" scoped>
.chat-container {
    width: 800px;
    height: 600px;
    margin: 20px auto;
    border-color: #e0e0e0; // 边框颜色调整为浅灰
    border-radius: 16px;
    background: white;
    display: flex;
    flex-direction: column;

    .message-list {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        height: calc(100vh - 200px);
        /* 根据实际布局调整 */
        /* 隐藏滚动条方案（全浏览器兼容） */
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE/Edge

        &::-webkit-scrollbar {
            display: none; // Chrome/Safari
            width: 0 !important; // 确保隐藏
            height: 0 !important;
        }

        // 解决隐藏滚动条后的布局问题
        padding-right: 16px !important; // 补偿滚动条消失的空间
        margin-right: -16px !important; // 负边距抵消补偿

        .message-item {
            width: 98%;
            margin-bottom: 25px;

            .bubble {
                max-width: 75%;
                padding: 12px 18px;
                position: relative;
                background: transparent; // 移除AI消息背景
                color: #333; // 文字颜色改为深灰
                // AI消息（左侧）
                // margin-right: auto;
                font-size: 16px;
                border-radius: 12px;
                box-shadow: none; // 移除阴影
                border: 1px solid #f0f0f0; // 添加浅灰色边框

                /* 新增折行样式 */
                white-space: pre-wrap; // 保留换行符同时自动折行
                word-break: break-word; // 允许单词内断行
                overflow-wrap: anywhere; // 紧急情况下任意位置断行

                /* 差异化加载样式 */
                .local-loading {
                    color: #666;
                    padding: 8px;
                    background: #f8f9fa;
                    border-radius: 8px;
                }

                .global-loading {
                    color: #409eff;
                    padding: 8px;
                    background: #e8f4ff;
                    border-radius: 8px;
                }

                .loading-text {
                    display: inline-block;
                    animation: breath 1.5s infinite;
                }

                @keyframes breath {
                    0% {
                        opacity: 0.8;
                    }

                    50% {
                        opacity: 0.4;
                    }

                    100% {
                        opacity: 0.8;
                    }
                }

                .action-buttons {
                    position: absolute;
                    left: -8px;
                    bottom: -28px;
                    display: flex;
                    gap: 8px;
                    opacity: 0;
                    transition: opacity 0.3s;
                    padding: 4px 8px;
                    border-radius: 24px;

                    &.always-visible,
                    .message-item:hover & {
                        opacity: 1;
                        visibility: visible;
                    }

                    .action-button-wrapper {
                        position: relative;
                        .el-button {
                            padding: 2px;
                            color: #666 !important;
                            &:hover {
                                color: #333 !important;
                                background: #f5f5f5;
                            }
                            &:hover + .button-text {
                                opacity: 1;
                                visibility: visible;
                                transform: translateY(0);
                            }
                        }
                        .button-text {
                            position: absolute;
                            left: 50%;
                            bottom: -25px;
                            transform: translateX(-50%) translateY(5px);
                            font-size: 12px;
                            color: #666;
                            white-space: nowrap;
                            opacity: 0;
                            visibility: hidden;
                            transition: all 0.2s ease;
                            background: rgba(255, 255, 255, 0.95);
                            padding: 4px 8px;
                            border-radius: 4px;
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                            z-index: 9999;
                            pointer-events: none;
                        }
                        &:hover .button-text {
                            opacity: 1;
                            visibility: visible;
                            transform: translateX(-50%) translateY(0);
                        }
                    }
                }

                .timestamp {
                    font-size: 12px;
                    color: #999; // 时间戳颜色调整
                    margin-top: 6px;
                    display: block;
                    text-align: right;
                }
            }

            // 修改思考气泡样式
            .thinking-bubble {
                max-width: 100% !important; // 覆盖默认的 max-width
                width: 88% !important; // 设置宽度为100%
                background: #f5f7fa !important;
                border-color: #e4e7ed !important;
                position: relative;
                overflow: hidden;
                margin: 0 !important; // 移除外边距

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg,
                            rgba(255, 255, 255, 0) 0%,
                            rgba(255, 255, 255, 0.6) 50%,
                            rgba(255, 255, 255, 0) 100%);
                    animation: wave 1.5s infinite;
                }
            }

            .thinking-text {
                display: flex;
                align-items: center;
                gap: 8px;
                position: relative; // 确保在波浪层上方
                z-index: 1;

                span {
                    font-size: 1.2em;
                    animation: float 2s ease-in-out infinite;
                }
            }

            .thinking-wave {
                position: relative;
                display: inline-block;
                color: #606266;

                &::after {
                    content: "...";
                    animation: dots 1.5s infinite;
                }
            }

            @keyframes wave {
                0% {
                    transform: translateX(-100%);
                }

                100% {
                    transform: translateX(100%);
                }
            }

            @keyframes float {

                0%,
                100% {
                    transform: translateY(0);
                }

                50% {
                    transform: translateY(-4px);
                }
            }

            @keyframes dots {

                0%,
                20% {
                    content: ".";
                }

                40% {
                    content: "..";
                }

                60%,
                100% {
                    content: "...";
                }
            }

            // 用户消息（右侧）
            &.user-message .bubble {
                font-size: 14px;

                margin-left: auto;
                background: #f0f0f0; // 用户消息背景改为灰色
                color: #333;
                border: none;
                border-radius: 18px 0 18px 18px;

                .timestamp {
                    color: #666;
                }

                .user-text {
                    margin: 10px 0; // 从5px增加到10px
                    display: inline-block;
                    line-height: 1.8; // 增加行高提升可读性
                }
            }

            &.ai-message .bubble {
                font-size: 16px; // AI 消息字体大小
                // background-color: #f1f1f1; // AI消息背景色
                padding: 10px;
                border-radius: 12px;
                position: relative;
                max-width: 88%;
                align-self: flex-start; // AI消息左对齐

                .ai-text {
                    margin: 10px 0; // 从5px增加到10px
                    display: inline-block;
                    line-height: 1.6; // 增加行高提升可读性
                }

                .markdown-body {
                    font-size: 14px;
                    line-height: 1.6;

                    :deep(p) {
                        margin: 8px 0;
                    }

                    :deep(pre) {
                        background-color: #f6f8fa;
                        border-radius: 6px;
                        padding: 16px;
                        overflow: auto;
                        font-size: 85%;
                        line-height: 1.45;
                        margin: 12px 0;
                    }

                    :deep(code) {
                        background-color: rgba(175, 184, 193, 0.2);
                        border-radius: 6px;
                        padding: 0.2em 0.4em;
                        font-size: 85%;
                        font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
                    }

                    :deep(ul),
                    :deep(ol) {
                        padding-left: 2em;
                        margin: 8px 0;
                    }

                    :deep(blockquote) {
                        padding: 0 1em;
                        color: #656d76;
                        border-left: 0.25em solid #d0d7de;
                        margin: 12px 0;
                    }

                    :deep(table) {
                        border-collapse: collapse;
                        width: 100%;
                        margin: 12px 0;

                        th,
                        td {
                            border: 1px solid #d0d7de;
                            padding: 6px 13px;
                        }

                        tr:nth-child(2n) {
                            background-color: #f6f8fa;
                        }
                    }
                }
            }
        }
    }

    .input-footer {
        padding: 16px;
        background: white;

        .custom-input-container {
            width: 100%;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #dcdfe6;
            border-radius: 20px;
            padding: 10px 10px 8px;
            display: flex;
            flex-direction: column;
            min-height: 90px;

            &:hover {
                border-color: #c0c4cc;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            }

            &:focus-within {
                border-color: #409eff;
                box-shadow: 0 2px 12px rgba(32, 160, 255, 0.2);
            }

            .custom-input {
                :deep(.el-textarea__inner) {
                    border: none;
                    padding: 0 0 20px 0 !important;
                    box-shadow: none;
                    line-height: 1.5;
                    min-height: 70px !important;
                    scrollbar-width: none;
                    -ms-overflow-style: none;

                    &::-webkit-scrollbar {
                        display: none;
                    }
                }
            }

            .action-buttons {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-top: 10px;

                .button-group {
                    display: flex;
                    align-items: center;
                    gap: 15px;

                    .icon-btn {
                        padding: 6px;
                        width: 30px;
                        height: 30px;
                        border: none;

                        &:hover {
                            background-color: #fff;
                        }

                        .icon-img {
                            width: 30px;
                            height: 30px;
                        }
                    }

                    .divider {
                        width: 1px;
                        height: 30px;
                        background-color: #dcdfe6;
                    }
                }
            }
        }
    }
}

// 可以添加光标闪烁效果
.ai-text.typing::after {
    content: '|';
    animation: blink 0.7s infinite;
}

@keyframes blink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}
</style>