<template>
    <el-dialog v-model="dialogVisible" class="realVoice_contact" width="888px" :style="{transformOrigin: 'center',}">
        <template #default>
            <div class="realVoice_contact_container">
                <div class="realVoice_contact_free_voice">
                    <div class="realVoice_contact_free_voice_text">
                        <span>免费试音<span class="time">{{user.time}}</span>分钟极速配音</span>
                    </div>  
                </div>
                <div class="realVoice_contact_information">
                    <div class="realVoice_contact_close">
                        <img src="@/assets/images/realVoice/close.png" @click="dialogVisible=false" alt="">
                    </div>
                        <div class="realVoice_contact_information_top">
                            <!-- <div class="realVoice_contact_information_top_img">
                                <img src="" alt="">
                            </div>
                            <div class="realVoice_contact_information_top_text">
                                <div class="realVoice_contact_information_top_name">
                                   {{user.name}}
                                </div>
                                <div class="realVoice_contact_information_top_rank">
                                    {{user.rank}}
                                </div>
                            </div> -->
                            扫码添加客服
                        </div>
                        <div class="realVoice_contact_information_qrcode">
                            <div class="realVoice_contact_information_qrcode_img">
                                <img :src="qrCodeImage" alt="">
                            </div>
                        </div>
                        <div class="realVoice_contact_information_describe">
                            <img src="@/assets/images/realVoice/realVoice_contact_information_describe_text.svg" alt="">
                        </div>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { tr } from 'element-plus/es/locale/index.mjs';
import { ref, defineExpose, reactive, computed } from 'vue';
import { useRoute } from 'vue-router';

// 导入二维码图片
import freeTrialSoundErcode from '@/assets/images/realVoice/free_trial_sound_ercode.png';
// import contactErcode from '@/assets/images/realVoice/contact_new.png';
const contactErcode = 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17536909926035.png';

let rate=ref(window.innerWidth/1920)
let dialogVisible = ref(false);
let user=reactive({
    time:5,
    name:'章若楠',
    rank:'专业配音员',
    qrImg:'',
})

// 获取当前路由信息
const route = useRoute();

// 判断是否为API服务页面
const isApiServicePage = computed(() => {
    return route.name === 'apiService' || route.name === 'documentation';
});

// 根据路由动态设置二维码图片
const qrCodeImage = computed(() => {
    return isApiServicePage.value ? freeTrialSoundErcode : contactErcode;
});

defineExpose({
    dialogVisible,
});
</script>

<style lang="scss">
.realVoice_contact{
    padding: 0;
    .el-dialog__header{
        display: none;
    }
.realVoice_contact_container {
    display: flex;
   
    .realVoice_contact_free_voice{
        width: 336px;
        background-position: 0 0;
        background-size: cover;
        background-repeat: no-repeat;
        padding-top: 56px;
        display: flex;
        justify-content: center;
        background-image: url('@/assets/images/realVoice/free_voice.png');
        .realVoice_contact_free_voice_text{
            font-size: 18px;
            color: #fff;
            display: flex;
            line-height: 28px;
            height: 28px;
            align-items: center;
            justify-content: center;
            .time{
                font-size: 28px;
                color: #18FF90;
            }
        }
        
    }
    .realVoice_contact_information{
        padding: 48px;
        padding-bottom: 113px;
        box-sizing: border-box;
        position: relative;
        flex: 1;
        .realVoice_contact_close{
            position: absolute;
            z-index: 1;
            top: 18px;
            right: 17px;
            width: 13px;
            height: 13px;
            cursor: pointer;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .realVoice_contact_information_top{
            // display: flex;
            // align-items: center;
            // margin-bottom: 24px;
            // .realVoice_contact_information_top_img{
            //     width: 47px;
            //     height: 47px;
            //     border-radius: 50%;
            //     overflow: hidden;
            //     margin-right: 12px;
            //     img{
            //         width: 100%;
            //         height: 100%;
            //     }
            // }
            // .realVoice_contact_information_top_text{
            //     display: flex;
            //     flex-direction: column;
            //     .realVoice_contact_information_top_name{
            //         font-size: 14px;
            //         line-height: 20px;
            //         margin-bottom: 4px;
            //     }
            //     .realVoice_contact_information_top_rank{
            //         width: 59px;
            //         height: 17px;
            //         background: rgba(10,175,96,0.1);
            //         border-radius: 100px 100px 100px 100px;
            //         border: 1px solid #0AAF60;
            //         font-size: 9px;
            //         color: #0AAF60;
            //         display: flex;
            //         align-items: center;
            //         justify-content: center;
            //     }
            // }
            color: #414651;
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 24px;
            
        }
        .realVoice_contact_information_qrcode{
            width: 100%;
            height: 234px;
            background: transparent;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            .realVoice_contact_information_qrcode_img{
                width: 128px;
                height: 128px;
                overflow: hidden;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .realVoice_contact_information_describe{
            display: flex;
            justify-content: center;
            font-size: 12px;
            color: rgba(0,0,0,0.45);
            align-items: center;
            img{
                width: 456px;
                height: 20px;
            }
        }
    }
}
}
</style>
