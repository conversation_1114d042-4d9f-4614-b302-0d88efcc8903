<template>
    <el-dialog v-model="dialogVisible" class="dubbing_selection_dialog" width="1068px" :show-close="false"
         append-to="#app">
        <template #header>
            <div class="dubbing_selection_dialog_header">
                <div class="dubbing_selection_dialog_header_title">配音选择</div>
                <img src="@/assets/images/digitalHuman/dubbing_selection_dialog_header_close.svg"
                    class="dubbing_selection_dialog_header_close" @click="dubbing_selection_dialog_close" alt="">
            </div>
        </template>
        <template #default>
            <div class="dubbing_selection_dialog_content">
                <!--选项卡和搜索  -->
                <div class="dubbing_selection_dialog_content_option_serach">
                    <div class="dubbing_selection_dialog_content_option">
                        <el-button v-for="item in option_list" :key="item.id"
                            :class="{ 'current': item.id == option_active }"
                            @click="changeNav(item.id)">{{ item.name }}</el-button>
                    </div>
                    <search ref="search_ref" @search_list="search_list"></search>
                </div>
                <!-- 导航列表 -->
                <navList ref="nav_list_ref" @changesubNav="changesubNav" v-if="option_active==1"></navList>
                <div class="dubbing_selection_dialog_content_nav" v-else>
                    <div class="dubbing_selection_dialog_content_nav_list" >
                        <div class="dubbing_selection_dialog_content_nav_item" v-for="(item, index) in my_timbre_nav" :key='index'  :class="{ 'current': selectetMycurrent == item.name }"  @click="selectetMyClick(item.name)">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <!-- 配音列表 -->
                <list ref="list_ref"></list>
            </div>
        </template>
        <template #footer>
            <div class="dubbing_selection_dialog_btns">
                <el-button @click="cancel" class="dubbing_selection_dialog_btns_cancel">取消</el-button>
                <el-button @click="submit" class="dubbing_selection_dialog_btns_submit">确定</el-button>
            </div>
        </template>

    </el-dialog>
</template>
<script setup>
import { getCurrentInstance, nextTick, onMounted,defineProps,reactive, ref, watch,defineExpose,defineEmits } from 'vue'
import navList from "@/views/modules/digitalHuman/components/right_operate/input_text/dubbing_selection/nav_list.vue"
import list from "@/views/modules/digitalHuman/components/right_operate/input_text/dubbing_selection/list.vue"
import search from "@/views/modules/digitalHuman/components/right_operate/input_text/dubbing_selection/search.vue"
import axios from 'axios';
import { useloginStore } from '@/stores/login'
import { ElMessage } from "element-plus";
let { proxy } = getCurrentInstance();
let loginStore = useloginStore()
let dialogVisible = ref(false)
let cancelNavRequest=ref(null)
let selectetMycurrent=ref('')
let search_ref=ref(null)
let choose_current_obj=ref(null)
let props = defineProps({
  apiMap: Object
})
let dubbing_selection_dialog_close = () => {
    dialogVisible.value = false
}
let option_list = reactive([
    {
        name: '全部音色',
        id: 1
    },
    {
        name: '我的音色',
        id: 2
    }
])
let navKeys = ref([{
    key: 'grade',
    nav_key: 'grade',
    arr_key: 'grade',
}, {
    key: 'gender',
    nav_key: 'genders',
    arr_key: 'gender',
}, {
    key: 'sceneCategory',
    nav_key: 'sceneMetadata',
    arr_key: 'scene_category',
}, {
    key: 'recommendTags',
    nav_key: 'recommend_tags',
    arr_key: 'recommend_tags',
}])
let my_timbre_nav= ref([
	{
		name: '收藏', selected: false, visible: true, level: 0,
	},{
		name: '已购', selected: false, visible: true, level: 0,
	},{
		name: '历史', selected: false, visible: true, level: 0,
	},{
		name: '克隆音色', selected: false, visible: true, level: 0,
	}
])
let option_active = ref(1)
let emit=defineEmits(['submit'])
let changeNav = async(id) => {
    option_active.value = id
    await nextTick()
    list_ref.value.close_aduio()
    list_ref.value.init([])
    if(id==1){
        changeList()
        list_ref.value.current_nav={
            option_active:id,
        }
    }else{
        if(choose_current_obj.value){
            selectetMyClick(choose_current_obj.value.selectetMycurrent)
        }else{
            selectetMyClick(my_timbre_nav.value[0].name)
        }
     
    }
    console.log('changeNav');
    
 
}
let nav_list_ref = ref(null)
let list_ref = ref(null)
let listData = ref([])//存储列表数据
let changeList = async () => {
    if (option_active.value == 1) {
        await nextTick()
        nav_list_ref.value.loading = true
        let list_data = await getList()
        let nav_list = getNav(list_data)
        nav_list_ref.value.uniqueArray = nav_list
        nav_list_ref.value.selectLastIndex = null
        try {
            render_nav(nav_list)
        } catch (error) {
            if (error.message) {

                ElMessage({ message: error.message, type: 'error' });
            }
        }
        nav_list_ref.value.loading = false
        adjustDialogPosition()
    }
}
let  sortListData=(data)=>{
  // 先复制一份数据，避免修改原数组
  const list = JSON.parse(JSON.stringify(data));

  const languageSkillsSort = ["中文", "英文"];
  const membershipGradeSort = ["SVIP", "VIP"];

  list.sort((a, b) => {
    // 语言排序（如果需要，取消注释）
    // const langA = languageSkillsSort.indexOf(a.languageSkills);
    // const langB = languageSkillsSort.indexOf(b.languageSkills);
    // if (langA !== langB) {
    //   return langA - langB;
    // }

    // 会员等级排序
    const gradeA = membershipGradeSort.indexOf(a.membershipGrade);
    const gradeB = membershipGradeSort.indexOf(b.membershipGrade);
    if (gradeA !== gradeB) {
      return gradeA - gradeB;
    }

    // 推荐度排序，值小的在前
    return a.recommendDegree - b.recommendDegree;
  });

  return list;
}
let getList = async (nav = {}, type = '') => {
    return new Promise(async (resolve, reject) => {
        await nextTick()
        list_ref.value.close_aduio()
        // 如果之前的请求存在，则取消它
        if (list_ref.value.cancelToken) {
            list_ref.value.cancelToken.cancel(' ');
        }
        // 创建新的 CancelToken 源并存储在 list_ref 中
        list_ref.value.cancelToken = axios.CancelToken.source();
        list_ref.value.loading = true
        console.log(444);

        try {
            let list_single_data = []
            let apiInfo =list_single_data = props.apiMap['全部音色']
            if (!apiInfo) return
            list_single_data = await apiInfo.fn({...apiInfo.params,cancelToken: list_ref.value.cancelToken.token})
            // let languageSkillsSort = ["中文", "英文"];
            // let membershipGradeSort = ["SVIP", "VIP"];
            // list_single_data.sort((a, b) => {
            //     // 先按语言技能排序
            //     // const langA = languageSkillsSort.indexOf(a.languageSkills);
            //     // const langB = languageSkillsSort.indexOf(b.languageSkills);
            //     // if (langA !== langB) {
            //     //     return langA - langB;
            //     // }
            //     // 语言相同的情况下按会员等级排序
            //     const gradeA = membershipGradeSort.indexOf(a.membershipGrade);
            //     const gradeB = membershipGradeSort.indexOf(b.membershipGrade);
            //     if (gradeA !== gradeB) {
            //         return gradeA - gradeB;
            //     }
            //     // 最后按 recommendDegree 排序，值小的在前
            //     return a.recommendDegree - b.recommendDegree;
            // });
            // let all_data = list_single_data
            // console.log(all_data, '列表渲染2');
            let all_data = sortListData(list_single_data);
            try {
                list_ref.value.init(JSON.parse(JSON.stringify(all_data)));
                if(choose_current_obj.value){
                    list_ref.value.init_choose(choose_current_obj.value)
                    choose_current_obj.value=null
                }
                // 这里是异步加载成功后的代码
            } catch (error) {
                console.error('调用 init 出错:', error);
                // 这里处理异常，比如提示用户或重试
            }
            search_ref.value.list=JSON.parse(JSON.stringify(all_data))
            listData.value = JSON.parse(JSON.stringify(all_data))
            console.log(all_data, '列表渲染3');
            resolve(all_data)

        } catch (error) {
            list_ref.value.init([])
            list_ref.value.loading = false
            // console.log(error,'error');
            // if(error.response.data){
            //     ElMessage({ message:error.response.data , type: 'error' });
            // }
            // ElMessage.error('数据加载失败，请刷新页面重试');
            if (axios.isCancel(error)) {
                console.log('请求被取消:', error.message); // 处理请求被取消的情况
            } else {
                // console.error('请求失败:', error); // 处理其他错误
            }
        }
    })
}
let getNav = (list_data) => {
    const extractUnique = (data, key, transformFunc) => {
        const uniqueSet = new Set();
        data.forEach(item => {
            // 获取原始值
            const value = transformFunc(item);
            if (value) {
                // 如果值包含逗号，则分割并添加到集合中
                value.split('、').forEach(val => {
                    uniqueSet.add(val.trim()); // 去掉空格并添加到集合中
                });
            }
        });
        return Array.from(uniqueSet).map(value => ({ [key]: value }));
    };


    // 使用 Set 来存储唯一的 gender 和 ageGroup
    const uniqueGenders = new Set();

    // 处理所有层
    list_data.forEach(item => {
        // 分割 gender 和 ageGroup
        const genders = item.gender.split('、');
        const ageGroups = item.ageGroup.split('、');

        // 将每个分割后的值添加到 uniqueGenders
        genders.forEach(gender => {
            uniqueGenders.add(gender.trim());
        });

        ageGroups.forEach(ageGroup => {
            uniqueGenders.add(ageGroup.trim());
        });
    });

    // 将唯一的值转换为对象并存入 the_data.genders
    const the_data = {};
    the_data.genders = Array.from(uniqueGenders).map(gender => ({ gender }));

    // 根据 list_data 中的顺序提取 genderOrder，并去重
    const genderOrder = Array.from(new Set(list_data.flatMap(item => item.gender.split('、').map(g => g.trim()))));

    // 定义 the_genders
    let the_genders = ['男', '女'];

    // 排序 genderOrder
    const sortedGenders = genderOrder.sort((a, b) => {
        const indexA = the_genders.indexOf(a);
        const indexB = the_genders.indexOf(b);

        // 如果在 the_genders 中找不到，返回 1，使其排到后面
        if (indexA === -1 && indexB === -1) return 0; // 都不在 the_genders 中
        if (indexA === -1) return 1; // a 不在 the_genders 中，排到后面
        if (indexB === -1) return -1; // b 不在 the_genders 中，排到后面

        return indexA - indexB; // 按照 the_genders 的顺序排序
    });
    // 将排序后的结果存入 the_data
    the_data.genders = sortedGenders.map(gender => ({ gender }));

    // 提取其他信息
    const sceneCategoryData = extractUnique(list_data, 'scene_category', item => item.sceneCategory);
    const recommendTagData = extractUnique(list_data, 'recommend_tags', item => item.recommendTags);
    const membershipGradeData = extractUnique(list_data, 'grade', item => item.membershipGrade);
    // console.log(sceneCategoryData,'sceneCategoryData');

    // 将提取的结果合并到 the_data
    the_data.sceneMetadata = sceneCategoryData;
    the_data.recommend_tags = recommendTagData;
    the_data.grade = membershipGradeData;
    return the_data
}
let render_nav = (nav_list) => {
    console.log('render_nav');
    nextTick(async () => {

        let { genders, sceneMetadata, grade, recommend_tags } = nav_list
        //    console.log(nav_list_ref.value,'tags');

        let tags = nav_list_ref.value.tags
        tags.map((item) => {
            Object.keys(item.condition).map((item1) => {
                item.condition[item1].arr = []
            })
        })
        let tagsData = {}
        let index1 = tags.findIndex((item) => item.type == nav_list_ref.value['current_type'])
        console.log(tags, index1, 'tags');

        tagsData = tags[index1]

        tagsData.condition[Object.keys(tagsData.condition)[0]].arr = grade
            .filter(item => item.grade !== '') // 过滤出不为空的项
            .map(item => {
                return {
                    key: item.grade,
                    label: item.grade,
                };
            });
        tagsData.condition[Object.keys(tagsData.condition)[1]].arr = genders
            .filter(item => item.gender !== '') // 过滤出不为空的项
            .map(item => {
                return {
                    key: item.gender,
                    label: item.gender,
                };
            });
        tagsData.condition[Object.keys(tagsData.condition)[2]].arr = sceneMetadata
            .filter(item => item.scene_category !== '') // 过滤出不为空的项
            .map(item => {
                return {
                    key: item.scene_category,
                    label: item.scene_category,
                };
            });
        tagsData.condition[Object.keys(tagsData.condition)[3]].arr = recommend_tags
            .filter(item => item.recommend_tags !== '') // 过滤出不为空的项
            .map(item => {
                return {
                    key: item.recommend_tags,
                    label: item.recommend_tags,
                };
            });

        nav_list_ref.value.uniqueArray = JSON.parse(JSON.stringify(tagsData.condition))
        Object.keys(nav_list_ref.value.uniqueArray).map((item) => {
            const exists = nav_list_ref.value.uniqueArray[item].arr.some(item => item.key === '全部');
            if (!exists) {
                nav_list_ref.value.uniqueArray[item].arr.unshift({
                    key: '全部',
                    label: '全部'
                })
            }

        })
        console.log(tagsData, 'tagsData');

        nav_list_ref.value.init()

    })
}
let init = async(data) => {
    console.log(data,'init');
    
    await nextTick()
    if(data){
        changeNav(data.option_active)
       
        choose_current_obj.value=data
    }else{
        changeNav(1)
    }

}
let changesubNav = (data) => {
    list_ref.value.close_aduio()
    handleClick(data)
    // getList(data,'changesubNav')
}
let handleClick = (filters) => {

    let selectLastIndex = nav_list_ref.value.selectLastIndex
    let selectedKeys = navKeys.value.slice(selectLastIndex + 1);
    selectedKeys.map((item) => {
        Object.keys(filters).map((item1) => {
            if (item.key == item1) {
                delete filters[item1]
            }
        })
    })
    list_ref.value.init([])
    nextTick(() => {
        const filteredData = filterData(listData.value, filters); // 筛选数据
        // console.log(filteredData,listData.value,filters,'filteredData');
        list_ref.value.init(JSON.parse(JSON.stringify(filteredData)))

        search_ref.value.list=JSON.parse(JSON.stringify(filteredData))
        let nav_data = getNav(filteredData); // 更新 the_data
        // render_nav(nav_data)
        nextTick(() => {
            let tags = nav_list_ref.value.tags
            let index1 = tags.findIndex((item) => item.type == nav_list_ref.value['current_type'])
            let tagsData = tags[index1]
            selectedKeys.map((item) => {
                tagsData.condition[item.key].arr = nav_data[item.nav_key]
                    .filter((item1) => item1[item.arr_key] !== '') // 过滤出 item1[item.arr_key] 不等于 ''
                    .map((item1) => {
                        return {
                            key: item1[item.arr_key],
                            label: item1[item.arr_key],
                        };
                    });
            });


            nav_list_ref.value.init()
            // render_nav(nav_data)
            console.log(tagsData, 'handleClick'); // 输出更新后的数据

        })
    })

};
let filterData = (data, filters) => {
    return data.filter(item => {
        return Object.keys(filters).every(key => {
            // 检查过滤条件是否存在
            const filterValue = filters[key]; // 直接获取过滤值
            if (!filterValue || filterValue === '') {
                return true; // 如果没有该条件，则跳过筛选
            }

            let itemValues = [];

            // 根据映射关系获取对应的 item 值
            if (key === 'gender') {
                itemValues = splitAndFilter(item.gender).concat(splitAndFilter(item.ageGroup));
            } else if (key === 'sceneCategory') {
                itemValues = splitAndFilter(item.sceneCategory);
            } else if (key === 'recommendTags') {
                itemValues = splitAndFilter(item.recommendTags);
            } else if (key === 'grade') {
                itemValues = splitAndFilter(item.membershipGrade);
            }

            // 如果当前过滤值为 '全部'，则不进行筛选
            if (filterValue === '全部') {
                return true; // 不筛选当前标签
            }

            // 检查 itemValues 是否包含 filterValue
            return itemValues.includes(filterValue);
        });
    });
};
let splitAndFilter = (value) => {
    // console.log(value,'splitAndFilter');
    if (value) {
        return value.split('、').map(item => item.trim());
    } else {
        return []
    }

};
let deepClone = (obj) => JSON.parse(JSON.stringify(obj))
let toCamelCase=(str)=>{
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}
let keysToCamelCase=(obj)=>{
  if (Array.isArray(obj)) {
    return obj.map(v => keysToCamelCase(v))
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = toCamelCase(key)
      acc[camelKey] = keysToCamelCase(obj[key])
      return acc
    }, {})
  }
  return obj
}
let selectetMyClick = async (data) => {
    console.log(data, 'selectetMyClick');
    
    await nextTick()
    list_ref.value.close_aduio()
    list_ref.value.init_choose()
  if (!loginStore.token) {
    proxy.$modal.open('组合式标题')
    return new Error('未登录')
  }
  list_ref.value.init([])
  selectetMycurrent.value = data
   list_ref.value.current_nav={
    option_active:option_active.value,
    selectetMycurrent:data
}
  const userId = JSON.parse(localStorage.getItem('user'))?.userId || ''
   // 如果之前的请求存在，则取消它
    if (list_ref.value.cancelToken) {
        list_ref.value.cancelToken.cancel(' ');
    }
   list_ref.value.cancelToken= axios.CancelToken.source()

  try {
    let res = null
    const cancelToken = list_ref.value.cancelToken.token
    let apiInfo = props.apiMap[data]
    if (!apiInfo) return
    res = await apiInfo.fn({...apiInfo.params,cancelToken:cancelToken})
    // switch (data) {
    //   case '已购':
    //     res = await queryUserBuyVoiceName({ userId, tts: 4 })
    //     break
    //   case '收藏':
    //     res = await bookmarkList({ userId, tts: 4, cancelToken })
    //     break
    //   case '历史':
    //     res = await queryUserUsedVoiceName({ userId, tts: 4, cancelToken })
    //     break
    //   default:
    //     res = await cloneList({ userId, cancelToken })
    //     break
    // }

    let listData = []

    if (data === '收藏' && Array.isArray(res) && res.length > 0) {
      listData = res
    } else if (res?.code === 0 && res?.data?.content?.result?.length > 0) {
      listData = res.data.content.result.map(item => {
        item.isPlay = false
        item.isSelected = false
        return { ...item, ...item.info }
      })
      if (data === '历史') {
        listData = listData.slice(0, 10)
      }
      listData = keysToCamelCase(listData)
    } else if (Array.isArray(res?.data) && res.data.length > 0) {
      listData = res.data
    }
    search_ref.value.list=JSON.parse(JSON.stringify(listData))
    if (listData.length > 0) {
      listData = listData.map(item => ({
        ...item,
        isPlay: false,
        isSelected: false
      }))
      list_ref.value.init(sortListData(listData))

    } else {
        list_ref.value.init([])
    }
    if(choose_current_obj.value){
        list_ref.value.init_choose(choose_current_obj.value)
        choose_current_obj.value=null
    }
    adjustDialogPosition()
    return res
  } catch (err) {
    console.error(err, 'err')
    list_ref.value.init([])
    return err
  }
}
let search_list=(data)=>{
    list_ref.value.close_aduio()
    setTimeout(()=>{
        list_ref.value.list=[]
        list_ref.value.list_loading=true
        // console.log('列表渲染1');
        list_ref.value.init(JSON.parse(JSON.stringify(data)))
        
    },300)

}
let cancel=()=>{
    dubbing_selection_dialog_close()
}
let submit=()=>{
    emit('submit',{
        character_id:list_ref.value.SoundItemId,
        option_active:option_active.value,
        selectetMycurrent:selectetMycurrent.value,
        info:list_ref.value.choose_timbre
    })
    cancel()
}
watch(()=>search_ref.value?.searchQuery, async(newValue, oldValue) => { 
    await nextTick()
    if(newValue!=oldValue){
        list_ref.value.input_search=newValue
    }

},{deep:true,immediate:true});
watch(dialogVisible, async(newValue, oldValue) => {
   if(!newValue){
    await nextTick()
    list_ref.value.close_aduio()
    list_ref.value.init_choose()
    search_ref.value.searchQuery=''

   
   }
});
let adjustDialogPosition=()=>{
  nextTick(() => {
    const dialogEl = document.querySelector('.dubbing_selection_dialog');
    if (!dialogEl) return;

    const dialogHeight = dialogEl.offsetHeight;
    const windowHeight = window.innerHeight;
    let appHeight = document.getElementById('app').clientHeight;
    let  scale = windowHeight / 953;
    let top=0
    // 计算居中 top，取整避免子像素
    top = Math.round((windowHeight - dialogHeight) / 2);
    if(windowHeight>=953){
        top = Math.round((windowHeight - dialogHeight) / 2);
    }else{
        top = Math.round((windowHeight/(windowHeight/953) - dialogHeight) / 2);
    }
    if(top<0){
        top=0
    }
console.log('top',windowHeight/(windowHeight/953),dialogHeight,top)
    // 设置 top，取消 transform
    dialogEl.style.top = `${top*scale*(appHeight/953)}px`;
    dialogEl.style.transform = 'none';
    dialogEl.style.transform = `scale(${appHeight/953})`;
    dialogEl.style.margin = '0 auto'; // 保持水平居中
  });
}

defineExpose({
    dialogVisible,
    init
})
</script>

<style lang="scss">
.dubbing_selection_dialog {
    padding: 0;
    border-radius: 8px;
    max-height: 893px;
    .el-dialog__header {
        padding: 0;

        .dubbing_selection_dialog_header {
            display: flex;
            align-items: center;
            padding: 18px 16px;
            width: 100%;
            box-sizing: border-box;
            border-bottom: 1px solid #DEDEDF;

            .dubbing_selection_dialog_header_title {
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
            }

            .dubbing_selection_dialog_header_close {
                width: 20px;
                height: 20px;
                margin-left: auto;
                cursor: pointer;
            }
        }
    }

    .dubbing_selection_dialog_content {
        padding: 20px 23px 0;

        .dubbing_selection_dialog_content_option_serach {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            .dubbing_selection_dialog_content_option {
                display: flex;
                align-items: center;
                border-radius: 2px;
                overflow: hidden;
                border: 1px solid #0AAF60;

                .el-button {
                    margin-left: 0;
                    padding: 0;
                    height: 30px;
                    width: 86px;
                    border-left: none;
                    border: none;

                    span {
                        font-size: 14px;
                        line-height: 18px;
                        color: #353D49;
                    }

                    &.current {
                        background-color: #0AAF60;
                        border: none;
                        border-radius: 2px;

                        span {
                            color: #fff;
                        }
                    }

                    &:first-child {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }

                    &:last-child {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                }
            }

         
        }
        .dubbing_selection_dialog_content_nav{
    display: flex;
    flex-direction: column;
    .dubbing_selection_dialog_content_nav_list {
        display: flex;
        flex-wrap: wrap;
       
    .dubbing_selection_dialog_content_nav_item {
            line-height: 22px;
            min-width:46px;
            padding: 2px 6px 2px 10px;
            margin-right: 11px;
            margin-bottom: 14px;
            font-size: 14px;
            line-height: 18px;
            color: #353D49;
            cursor: pointer;
            &.current{
                color:#0AAF60;
;
            }
        }
    }
}

    }
    .el-dialog__footer{
        padding: 0;
        .dubbing_selection_dialog_btns{
            padding: 16px 24px;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .el-button{
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 9px 34px;
                border-radius: 4px;
                cursor: pointer;
                border: none;
                span{
                    font-size: 14px;
                    line-height: 22px;
                    text-align: center;
                    letter-spacing: -0.01px;
                    color: #FFFFFF;
                }
                &.dubbing_selection_dialog_btns_cancel{
                    background: #D3D3D2;
                }
                &.dubbing_selection_dialog_btns_submit{
                    background-color:#0AAF60;
                }
                &+.el-button{
                    margin-left: 16px;
                }
            }
        }
    }
    

}
</style>