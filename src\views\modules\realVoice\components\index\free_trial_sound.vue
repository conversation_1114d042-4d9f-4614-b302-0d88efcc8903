<template>
    <el-dialog v-model="dialogVisible" class="free_trial_sound_dialog" width="410px" append-to="#app">
        <div  class="free_trial_sound">
        <img src="@/assets/images/realVoice/free_trial_sound_close.svg" class="free_trial_sound_close" @click="close" alt="">
        <div class="free_trial_sound_ercode">
            <img :src="qrCodeImage" alt="">
        </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, defineExpose, computed } from 'vue';
import { useRoute } from 'vue-router';

// 导入二维码图片
import freeTrialSoundErcode from '@/assets/images/realVoice/free_trial_sound_ercode.png';
import contactErcode from '@/assets/images/realVoice/contact.png';
let dialogVisible = ref(false);

// 获取当前路由信息
const route = useRoute();

// 判断是否为API服务页面
const isApiServicePage = computed(() => {
    return route.name === 'apiService' || route.name === 'documentation';
});

// 根据路由动态设置二维码图片
const qrCodeImage = computed(() => {
    return isApiServicePage.value ? freeTrialSoundErcode : contactErcode;
});

let close=()=>{
    dialogVisible.value = false;
}
defineExpose({
    dialogVisible,
});
</script>

<style lang="scss">
.free_trial_sound_dialog .el-dialog__body,
.free_trial_sound_dialog .el-dialog__header {
  padding-left: 0;
  padding-right: 0;
}
.free_trial_sound_dialog{
    background-color: transparent;
    box-shadow: none;
    padding: 0;
    .el-dialog__header{
        display: none;
    }
    .free_trial_sound{
     width: 100%;
    height: 479px;
    background-image: url('@/assets/images/realVoice/free_trial_sound_bg.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size:contain;
    
    // position: absolute;
    // top: 124px;
    // right: 0;
    // z-index:2;
    .free_trial_sound_close{
        position: absolute;
        top: 3px;
        right: 10px;
        z-index: 3;
        cursor: pointer;
        width: 33px;
        height: 33px;
    }
    .free_trial_sound_ercode{
        width: 184px;
        height: 184px;
        padding: 10px;
        box-sizing: border-box;
        position: absolute;
        top: 208px;
        left: 87px;
        border-radius: 12px;
        background-color: #fff;
        z-index: 4;
        img{
            width: 100%;
            height: 100%;
        }
    }
}
}

</style>
