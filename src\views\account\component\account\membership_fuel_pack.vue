<template>
    <div v-if="current_nav=='加油包'" class="description">*购买须知：字符加油包仅配音会员可购，且需配合VIP或SVIP会员使用，会员过期后字符包无法使用，续费开通会员后可继续使用。</div>
    <div v-else-if="current_nav=='算力'" class="description">*购买须知：购买须知：算粒仅适用于一键成片功能，合成视频时会根据合成时长消耗算粒。</div>
    <div v-else class="description">*购买须知：空间用于配音帮手平台所有线上素材的存储，AI配音、数字人、声音克隆、一键成片等多种功能皆可通用。</div>
    <div class="membership_fuel_pack" v-loading="loading">
        <div class="membership_fuel_pack_item" v-for="(item,index) in member" :key="index" :class="current==item.id?'current':''" @click="choose(item.id)">
            <div class="membership_fuel_pack_item_top" :class="current_nav=='加油包'?'character':''">
                <div class="membership_fuel_pack_item_quantity">
                      {{ convertToNumber(item.volume) }}<span><template v-if="current_nav=='加油包'">字符</template><template v-else-if="current_nav=='算力'">算粒</template><template v-else><template v-if="index<=2">/单月</template><template v-else>/半年</template></template></span>
                </div>
                <div class="membership_fuel_pack_item_price">
                    {{ item.totalPrice }}
                </div>
            </div>
            <div class="membership_fuel_pack_item_describe" v-if="current_nav=='加油包'">
                VIP、SVIP音色可用
            </div>
            <button class="membership_fuel_pack_item_btn" @click="pay(item)">立即购买</button>
        </div>
    </div>
    <!-- 会员计划支付弹窗 -->
    <memberShipPayDialog ref="member_ship_pay_dialog_ref" @update_code="update_code"></memberShipPayDialog>
</template>
<script setup>
 import {ref, reactive,defineExpose,getCurrentInstance } from 'vue'
 import memberShipPayDialog from "./member_ship_pay_dialog.vue"
 import {ordersCreate} from '@/api/account.js'
import {accAdd,accSub,accMul,accDiv} from "@/utils/accuracy.js"
import { useloginStore } from '@/stores/login'
import { useUmeng } from '@/utils/umeng/hook'
import { ElMessage } from "element-plus";
let loginStore = useloginStore()
const { proxy } = getCurrentInstance();
const umeng = useUmeng()
let member=ref([
    
])
let loading=ref(false)
let member_ship_pay_dialog_ref=ref(null)
let current_nav=ref('')
let current=ref('')
let choose=(id)=>{
    current.value=id
}
let pay_data=ref()
//更新付款码
let update_code=()=>{
    return new Promise(async(resolve,reject)=>{
        let item=pay_data.value
        let data=await ordersCreate({
            paymentType:'PURCHASE',
            planId:item.id,
            quantity:1,
            userId:loginStore.userId
        })
        console.log(get_price(data.resp_data.total_amount,100),'价格');
        
        member_ship_pay_dialog_ref.value.user.price=get_price(data.resp_data.total_amount,100)   
        member_ship_pay_dialog_ref.value.user.qrcode=data.resp_data.counter_url
        member_ship_pay_dialog_ref.value.order_params=data
        resolve(true)
    })
}
let pay=async(item)=>{
    if(!loginStore.token||loginStore.token==''){
        proxy.$modal.open('组合式标题')
        return 
    }
    if(current_nav.value=='加油包'&&loginStore.memberInfo&&loginStore.memberInfo.level&&loginStore.memberInfo.level.level==0){
        ElMessage({message:'请成为会员后购买加油包', type: 'error' });
        return 
    }
    // 添加埋点代码
    umeng.trackEvent(
      '会员服务', 
      '点击立即购买', 
      `${current_nav.value}-${convertToNumber(item.volume)}${current_nav.value === '加油包' ? '字符' : current_nav.value === '算力' ? '算粒' : ''}-用户ID:${loginStore.userId || ''}`, 
      item.totalPrice.toString()
    )
    
    pay_data.value=item
    await update_code()
    member_ship_pay_dialog_ref.value.current_page='membership_fuel_pack'
    member_ship_pay_dialog_ref.value.dialogVisible=true
    
}
let convertToNumber=(amount)=>{

        if (amount.includes("万")) {
            const number = parseFloat(amount) * 10000;
            return number;
        } else {
            return amount; // 或者可以选择返回原始值 amount
        }
}
let get_price=(a,b)=>{
    return accDiv(a,b)
}   
defineExpose({
    member,
    current,
    loading,
    current_nav
})
</script>
<style lang="scss" scoped>
.description{
    width: 100%;
    padding:0 10%;
    font-size: 16px;
    color: #0AAF60;
}
.membership_fuel_pack{
    display: flex; 
    justify-content:flex-start; 
    flex-wrap: wrap;
    width: 100%;
    padding:0 10%;
    padding-bottom: 166px;
    .membership_fuel_pack_item{
        margin: 19px;
        // height: 252px;
        padding: 32px 24px ;
        box-shadow: 0px 0px 35px 0px rgba(0,0,0,0.05);
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 24px;
        flex: 0 1 calc(33.33% - 38px);
        cursor: pointer;
        .membership_fuel_pack_item_top{
            display: flex;
            justify-content: center;
            margin-bottom: 72px;
            .membership_fuel_pack_item_quantity{
                font-size: 46px;
                color: #000000;
                height: 66px;
                font-style: italic;
                span{
                    font-size: 24px;
                    color: #000000;
                }
            }
            .membership_fuel_pack_item_price{
                margin-left: auto;
                font-size: 20px;
                color: #000000;
                display: flex;
                align-items: center;
            }
            &.character{
                margin-bottom: 3px; 
            }
        }

        .membership_fuel_pack_item_describe{
            font-size: 14px;
            line-height: 30px;
            color: #353D49;
            margin-bottom: 39px;
        }
        .membership_fuel_pack_item_btn{
            width: 100%;
            height: 50px;
            background: #353D49;
            border-radius: 8px;
            font-size: 16px;
            color: #FFFFFF;
            border: none;
            cursor: pointer;
        }
        &:nth-child(3n){
            margin-right: 0;
        }
        &:nth-child(3n + 1) {
            margin-left: 0; 
        }

        &.current{
            box-shadow: 0px 0px 35px 0px rgba(50,98,0,0.1);
            border: 1px solid;
            border-image: linear-gradient(134deg, rgba(188, 239, 86, 1), rgba(55, 211, 41, 1)) 1 1;
           
            .membership_fuel_pack_item_btn{
                // background: linear-gradient( 340deg, #BCEF56 0%, #37D329 100%);
                border: none;
                background: linear-gradient(272.52deg, #BCEF56 -24.47%, #37D329 97.1%);
            }
        }        
    }
}
</style>